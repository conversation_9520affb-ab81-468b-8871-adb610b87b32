#!/bin/bash

# Build frontend assets locally to avoid Docker build hanging
# This script builds the frontend on the host machine first

set -e

echo "Building Superset frontend locally..."

cd superset-frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing npm dependencies..."
    npm ci --prefer-offline --no-audit --no-fund
fi

# Build frontend
echo "Building frontend assets..."
NODE_OPTIONS="--max_old_space_size=6144" npm run build

echo "Frontend build completed!"
echo "Assets available in: superset-frontend/dist/"

# Check if build was successful
if [ -d "dist" ] && [ "$(ls -A dist)" ]; then
    echo "✅ Frontend build successful"
    ls -la dist/
else
    echo "❌ Frontend build failed - no dist directory or empty"
    exit 1
fi