#!/bin/bash

echo "🚀 Building and running MLISA Data Studio..."

# Ensure we're in the project root
cd "$(dirname "$0")/.."

# Copy environment file if it doesn't exist
if [ ! -f "mlisa/docker/.env" ]; then
    echo "📋 Copying environment file..."
    cp mlisa/docker/.env-mlisa mlisa/docker/.env
fi

# Build the image
echo "🔨 Building Docker image..."
docker build -t mlisa-data-studio -f mlisa/docker/Dockerfile.mlisa .

# Run the containers
echo "🚀 Starting MLISA Data Studio..."
docker-compose -f mlisa/docker/docker-compose-mlisa.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Show logs
echo "📋 Showing logs..."
docker-compose -f mlisa/docker/docker-compose-mlisa.yml logs -f