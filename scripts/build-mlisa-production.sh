#!/bin/bash

# MLISA Data Studio Production Build Script
# This script builds a production-ready Docker image for MLISA Data Studio

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PROD_ENV_FILE="$PROJECT_ROOT/mlisa/docker/.env-mlisa-prod"
COMPOSE_FILE="$PROJECT_ROOT/mlisa/docker/docker-compose-mlisa-prod.yml"

echo -e "${BLUE}🚀 MLISA Data Studio Production Build${NC}"
echo "=================================================="

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi

    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed. Please install docker-compose first."
        exit 1
    fi

    print_status "Prerequisites check passed"
}

# Validate production environment file
validate_env_file() {
    echo -e "${BLUE}🔧 Validating production environment...${NC}"

    if [[ ! -f "$PROD_ENV_FILE" ]]; then
        print_error "Production environment file not found: $PROD_ENV_FILE"
        print_warning "Please copy and customize the production environment template."
        exit 1
    fi

    # Check for placeholder values that need to be changed
    if grep -q "CHANGE_ME" "$PROD_ENV_FILE"; then
        print_error "Production environment file contains placeholder values!"
        print_warning "Please update all 'CHANGE_ME' values in $PROD_ENV_FILE"
        echo -e "Values that need to be updated:"
        grep "CHANGE_ME" "$PROD_ENV_FILE" | head -5
        exit 1
    fi

    # Check for critical configuration
    critical_vars=("SECRET_KEY" "DATABASE_PASSWORD")
    for var in "${critical_vars[@]}"; do
        if ! grep -q "^${var}=" "$PROD_ENV_FILE"; then
            print_warning "$var is not set in the environment file"
        fi
    done

    print_status "Environment validation passed"
}

# Build production images
build_production_images() {
    echo -e "${BLUE}🔨 Building production Docker images...${NC}"

    cd "$PROJECT_ROOT"

    # Clear any existing build cache for clean production build
    print_status "Clearing Docker build cache..."
    docker builder prune -f

    # Build with BuildKit for better performance
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1

    print_status "Starting production build (this may take 10-15 minutes)..."

    if docker-compose -f "$COMPOSE_FILE" build --no-cache; then
        print_status "Production images built successfully"
    else
        print_error "Failed to build production images"
        exit 1
    fi
}

# Validate built images
validate_images() {
    echo -e "${BLUE}🔍 Validating built images...${NC}"

    # Check if the main image exists
    if docker images | grep -q "rsa-mlisa-data-studio-prod"; then
        print_status "Production image found"

        # Show image size
        image_size=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "rsa-mlisa-data-studio-prod" | head -1)
        echo "Image details: $image_size"
    else
        print_error "Production image not found"
        exit 1
    fi
}

# Run security checks
run_security_checks() {
    echo -e "${BLUE}🔒 Running security checks...${NC}"

    # Check if image runs as non-root user
    user_check=$(docker run --rm --entrypoint="" rsa-mlisa-data-studio-prod:latest whoami 2>/dev/null || echo "root")
    if [[ "$user_check" != "root" ]]; then
        print_status "Image runs as non-root user: $user_check"
    else
        print_warning "Image is running as root user (security concern)"
    fi

    # Check for sensitive files in the image
    print_status "Checking for sensitive files..."
    if docker run --rm --entrypoint="" rsa-mlisa-data-studio-prod:latest find /app -name "*.log" -o -name "*.pid" -o -name "*.sock" 2>/dev/null | grep -q .; then
        print_warning "Found potential temporary files in image"
    else
        print_status "No sensitive temporary files found"
    fi
}

# Generate deployment instructions
generate_deployment_instructions() {
    echo -e "${BLUE}📋 Generating deployment instructions...${NC}"

    cat > "$PROJECT_ROOT/PRODUCTION_DEPLOYMENT.md" << EOF
# MLISA Data Studio Production Deployment

## Pre-deployment Checklist

### 🔐 Security
- [ ] Updated all passwords in \`.env-mlisa-prod\`
- [ ] Generated secure SECRET_KEY using \`openssl rand -base64 42\`
- [ ] Configured HTTPS/SSL termination (use reverse proxy like nginx)
- [ ] Set up proper firewall rules
- [ ] Configured security headers

### 🗄️ Infrastructure
- [ ] External PostgreSQL database configured
- [ ] External Redis cache configured
- [ ] Persistent volumes for data storage
- [ ] Backup strategy implemented
- [ ] Monitoring and logging set up

### 🚀 Deployment Commands

1. **Build production images:**
   \`\`\`bash
   ./scripts/build-mlisa-production.sh
   \`\`\`

2. **Start production services:**
   \`\`\`bash
   docker-compose -f mlisa/docker/docker-compose-mlisa-prod.yml up -d
   \`\`\`

3. **Initialize database (first time only):**
   \`\`\`bash
   docker-compose -f mlisa/docker/docker-compose-mlisa-prod.yml run --rm superset-init
   \`\`\`

4. **Check service health:**
   \`\`\`bash
   docker-compose -f mlisa/docker/docker-compose-mlisa-prod.yml ps
   curl http://localhost:8088/health
   \`\`\`

### 📊 Monitoring

- Application: http://localhost:8088
- Health check: http://localhost:8088/health
- Logs: \`docker-compose -f mlisa/docker/docker-compose-mlisa-prod.yml logs -f\`

### 🔧 Maintenance

- **Update application:**
  \`\`\`bash
  ./scripts/build-mlisa-production.sh
  docker-compose -f mlisa/docker/docker-compose-mlisa-prod.yml up -d
  \`\`\`

- **Backup database:**
  \`\`\`bash
  docker exec superset_db_prod pg_dump -U superset superset > backup_\$(date +%Y%m%d_%H%M%S).sql
  \`\`\`

### ⚠️ Important Notes

1. **Never use the development configuration in production**
2. **Always use external databases and Redis in production**
3. **Set up SSL/TLS termination with a reverse proxy**
4. **Monitor resource usage and scale as needed**
5. **Regularly update dependencies and security patches**

EOF

    print_status "Deployment instructions generated: $PROJECT_ROOT/PRODUCTION_DEPLOYMENT.md"
}

# Main execution
main() {
    echo "Starting MLISA production build process..."
    echo "Project root: $PROJECT_ROOT"
    echo "Environment file: $PROD_ENV_FILE"
    echo "Compose file: $COMPOSE_FILE"
    echo ""

    check_prerequisites
    validate_env_file
    build_production_images
    validate_images
    run_security_checks
    generate_deployment_instructions

    echo ""
    echo -e "${GREEN}🎉 Production build completed successfully!${NC}"
    echo ""
    print_status "Next steps:"
    echo "1. Review the deployment instructions: PRODUCTION_DEPLOYMENT.md"
    echo "2. Update the production environment file if needed"
    echo "3. Deploy using: docker-compose -f $COMPOSE_FILE up -d"
    echo ""
    print_warning "Remember to configure external databases and SSL/TLS for production!"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "MLISA Data Studio Production Build Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --validate     Only validate environment (don't build)"
        echo "  --build-only   Only build images (skip validation)"
        echo ""
        echo "This script builds production-ready Docker images for MLISA Data Studio."
        exit 0
        ;;
    --validate)
        check_prerequisites
        validate_env_file
        print_status "Validation completed"
        exit 0
        ;;
    --build-only)
        check_prerequisites
        build_production_images
        validate_images
        print_status "Build completed"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac