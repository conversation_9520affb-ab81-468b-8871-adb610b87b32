# SHA1:c186006a3f82c8775e1039f37c52309f6c858197
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
-r base.txt
-e file:.
    # via
    #   -r requirements/base.in
    #   -r requirements/development.in
astroid==3.1.0
    # via pylint
boto3==1.34.112
    # via
    #   apache-superset
    #   dataflows-tabulator
botocore==1.34.112
    # via
    #   boto3
    #   s3transfer
build==1.2.1
    # via pip-tools
cached-property==1.5.2
    # via tableschema
cfgv==3.3.1
    # via pre-commit
chardet==5.1.0
    # via
    #   dataflows-tabulator
    #   tox
cmdstanpy==1.1.0
    # via prophet
contourpy==1.0.7
    # via matplotlib
coverage[toml]==7.2.5
    # via pytest-cov
cycler==0.11.0
    # via matplotlib
dataflows-tabulator==1.54.3
    # via tableschema
db-dtypes==1.2.0
    # via pandas-gbq
dill==0.3.8
    # via pylint
distlib==0.3.8
    # via virtualenv
docker==7.0.0
    # via apache-superset
et-xmlfile==1.1.0
    # via openpyxl
filelock==3.12.2
    # via
    #   tox
    #   virtualenv
flask-cors==4.0.0
    # via apache-superset
flask-testing==0.8.1
    # via apache-superset
fonttools==4.51.0
    # via matplotlib
freezegun==1.5.1
    # via apache-superset
future==0.18.3
    # via pyhive
gevent==24.2.1
    # via apache-superset
google-api-core[grpc]==2.11.0
    # via
    #   google-cloud-bigquery
    #   google-cloud-bigquery-storage
    #   google-cloud-core
    #   pandas-gbq
    #   sqlalchemy-bigquery
google-auth-oauthlib==1.0.0
    # via
    #   pandas-gbq
    #   pydata-google-auth
google-cloud-bigquery==3.20.1
    # via
    #   apache-superset
    #   pandas-gbq
    #   sqlalchemy-bigquery
google-cloud-bigquery-storage==2.19.1
    # via pandas-gbq
google-cloud-core==2.3.2
    # via google-cloud-bigquery
google-crc32c==1.5.0
    # via google-resumable-media
google-resumable-media==2.7.0
    # via google-cloud-bigquery
googleapis-common-protos==1.63.0
    # via
    #   google-api-core
    #   grpcio-status
grpcio==1.62.1
    # via
    #   apache-superset
    #   google-api-core
    #   grpcio-status
grpcio-status==1.60.1
    # via google-api-core
identify==2.5.36
    # via pre-commit
ijson==3.2.3
    # via dataflows-tabulator
iniconfig==2.0.0
    # via pytest
isort==5.12.0
    # via pylint
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
jsonlines==4.0.0
    # via dataflows-tabulator
jsonschema-spec==0.1.6
    # via openapi-spec-validator
kiwisolver==1.4.5
    # via matplotlib
lazy-object-proxy==1.10.0
    # via openapi-spec-validator
linear-tsv==1.1.0
    # via dataflows-tabulator
matplotlib==3.9.0
    # via prophet
mccabe==0.7.0
    # via pylint
mysqlclient==2.2.4
    # via apache-superset
nodeenv==1.8.0
    # via pre-commit
oauthlib==3.2.2
    # via requests-oauthlib
openapi-schema-validator==0.4.4
    # via openapi-spec-validator
openapi-spec-validator==0.5.6
    # via apache-superset
openpyxl==3.1.2
    # via dataflows-tabulator
pandas-gbq==0.19.1
    # via apache-superset
parameterized==0.9.0
    # via apache-superset
pathable==0.4.3
    # via jsonschema-spec
pillow==10.3.0
    # via
    #   apache-superset
    #   matplotlib
pip-compile-multi==2.6.3
    # via apache-superset
pip-tools==7.4.1
    # via pip-compile-multi
playwright==1.42.0
    # via apache-superset
pluggy==1.4.0
    # via
    #   pytest
    #   tox
pre-commit==3.7.1
    # via apache-superset
progress==1.6
    # via apache-superset
prophet==1.1.5
    # via apache-superset
proto-plus==1.22.2
    # via google-cloud-bigquery-storage
protobuf==4.23.0
    # via
    #   google-api-core
    #   google-cloud-bigquery-storage
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psycopg2-binary==2.9.6
    # via apache-superset
pure-sasl==0.6.2
    # via thrift-sasl
pydata-google-auth==1.7.0
    # via pandas-gbq
pydruid==0.6.9
    # via apache-superset
pyee==11.0.1
    # via playwright
pyfakefs==5.3.5
    # via apache-superset
pyhive[hive_pure_sasl]==0.7.0
    # via apache-superset
pyinstrument==4.4.0
    # via apache-superset
pylint==3.1.0
    # via apache-superset
pyproject-api==1.6.1
    # via tox
pyproject-hooks==1.0.0
    # via
    #   build
    #   pip-tools
pytest==7.4.4
    # via
    #   apache-superset
    #   pytest-cov
    #   pytest-mock
pytest-cov==5.0.0
    # via apache-superset
pytest-mock==3.10.0
    # via apache-superset
python-ldap==3.4.4
    # via apache-superset
requests-oauthlib==2.0.0
    # via google-auth-oauthlib
rfc3339-validator==0.1.4
    # via openapi-schema-validator
rfc3986==2.0.0
    # via tableschema
ruff==0.4.5
    # via apache-superset
s3transfer==0.10.1
    # via boto3
sqlalchemy-bigquery==1.11.0
    # via apache-superset
sqloxide==0.1.43
    # via apache-superset
statsd==4.0.1
    # via apache-superset
tableschema==1.20.10
    # via apache-superset
thrift==0.16.0
    # via
    #   apache-superset
    #   thrift-sasl
thrift-sasl==0.4.3
    # via apache-superset
tomlkit==0.12.5
    # via pylint
toposort==1.10
    # via pip-compile-multi
tox==4.6.4
    # via apache-superset
tqdm==4.66.4
    # via
    #   cmdstanpy
    #   prophet
trino==0.328.0
    # via apache-superset
tzlocal==5.2
    # via trino
unicodecsv==0.14.1
    # via
    #   dataflows-tabulator
    #   tableschema
virtualenv==20.23.1
    # via
    #   pre-commit
    #   tox
wheel==0.43.0
    # via pip-tools
xlrd==2.0.1
    # via dataflows-tabulator
zope-event==5.0
    # via gevent
zope-interface==5.4.0
    # via gevent

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
