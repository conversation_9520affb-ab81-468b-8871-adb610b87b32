#!/usr/bin/env bash

set -e

STEP_CNT=3

echo_step() {
cat <<EOF

######################################################################
Init Step ${1}/${STEP_CNT} [${2}] -- ${3}
######################################################################

EOF
}

echo_step "1" "Starting" "Applying DB migrations"

# Debug: Check Python environment and MLISA package
echo "=== DEBUGGING PYTHON ENVIRONMENT ==="
echo "Python version: $(python --version)"
echo "PYTHONPATH: $PYTHONPATH"
echo "Current working directory: $(pwd)"
echo "Contents of /app/mlisa:"
ls -la /app/mlisa/ || echo "Directory /app/mlisa not found"
echo "Python sys.path:"
python -c "import sys; [print(f'  {p}') for p in sys.path]"
echo "Testing MLISA import:"
python -c "import mlisa; print('✅ MLISA package imported successfully')" || echo "❌ Failed to import MLISA package"
echo "Testing MLISA config import:"
python -c "import mlisa.config; print('✅ MLISA config imported successfully')" || echo "❌ Failed to import MLISA config"
echo "=== END DEBUGGING ==="

superset db upgrade
echo_step "1" "Complete" "Applying DB migrations"

# Create an admin user
echo_step "2" "Starting" "Setting up admin user"
superset fab create-admin \
    --username admin \
    --firstname Superset \
    --lastname Admin \
    --email <EMAIL> \
    --password admin
echo_step "2" "Complete" "Setting up admin user"

# Load examples (optional)
echo_step "3" "Starting" "Loading examples"
if [ "$SUPERSET_LOAD_EXAMPLES" = "yes" ]; then
    superset load_examples
fi
echo_step "3" "Complete" "Loading examples"

echo "MLISA Data Studio initialized successfully."