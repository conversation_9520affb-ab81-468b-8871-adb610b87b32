#!/usr/bin/env bash

set -e

STEP_CNT=3

echo_step() {
cat <<EOF

######################################################################
Init Step ${1}/${STEP_CNT} [${2}] -- ${3}
######################################################################

EOF
}

echo_step "1" "Starting" "Applying DB migrations"
superset db upgrade
echo_step "1" "Complete" "Applying DB migrations"

# Create an admin user
echo_step "2" "Starting" "Setting up admin user"
superset fab create-admin \
    --username admin \
    --firstname Superset \
    --lastname Admin \
    --email <EMAIL> \
    --password admin
echo_step "2" "Complete" "Setting up admin user"

# Load examples (optional)
echo_step "3" "Starting" "Loading examples"
if [ "$SUPERSET_LOAD_EXAMPLES" = "yes" ]; then
    superset load_examples
fi
echo_step "3" "Complete" "Loading examples"

echo "MLISA Data Studio initialized successfully."