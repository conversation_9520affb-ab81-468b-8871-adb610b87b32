#
# MLISA Data Studio Production Environment Configuration
# ⚠️  IMPORTANT: Update all passwords, secrets, and URLs before deploying to production!
#

# =====================================================
# Database Configuration - Use external DB in production!
# =====================================================
DATABASE_DIALECT=postgresql
DATABASE_USER=superset
DATABASE_PASSWORD=CHANGE_ME_TO_SECURE_PASSWORD
DATABASE_HOST=db
DATABASE_PORT=5432
DATABASE_DB=superset

# Database URL (alternative to individual settings above)
# DATABASE_URL=**************************************/superset

# =====================================================
# Redis Configuration - Use external Redis in production!
# =====================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=changeme
REDIS_DB=1

# Alternative Redis URL
# REDIS_URL=redis://:changeme@redis:6379/1

# =====================================================
# Superset Core Configuration
# =====================================================
SUPERSET_ENV=production
FLASK_ENV=production

# 🔐 CRITICAL: Generate a strong secret key!
# Use: openssl rand -base64 42
SECRET_KEY=CHANGE_ME_TO_A_COMPLEX_RANDOM_SECRET_GENERATED_WITH_openssl_rand_base64_42

# =====================================================
# Security Configuration
# =====================================================
# Enable HTTPS in production
ENABLE_PROXY_FIX=True
WTF_CSRF_ENABLED=True
WTF_CSRF_TIME_LIMIT=None

# Session configuration
PERMANENT_SESSION_LIFETIME=1800
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# =====================================================
# Performance Configuration
# =====================================================
# Cache configuration
CACHE_TYPE=RedisCache
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=superset_

# Results backend
RESULTS_BACKEND_TYPE=redis
RESULTS_BACKEND_URL=redis://:changeme@redis:6379/2

# Query limits
SUPERSET_WEBSERVER_TIMEOUT=300
SQLLAB_TIMEOUT=300
SUPERSET_DEFAULT_PAGE_SIZE=100

# =====================================================
# SMTP Configuration (Update with your SMTP settings)
# =====================================================
SMTP_HOST=smtp.example.com
SMTP_STARTTLS=True
SMTP_SSL=False
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_PORT=587
SMTP_MAIL_FROM=<EMAIL>

# =====================================================
# MLISA Specific Configuration
# =====================================================
# Update these URLs to point to your actual services
MLISA_RBAC_URL=https://your-rbac-service.com
MLISA_NOTIFICATION_URL=https://your-notification-service.com
MLISA_SUPERSET_URL=https://your-superset-domain.com

# Split.io configuration (get these from Split.io dashboard)
SPLIT_IO_API_KEY=your_split_io_api_key_here
SPLIT_IO_ENVIRONMENT=production

# Feature flags
MLISA_FEATURE_RBAC_ENABLED=true
MLISA_FEATURE_NOTIFICATIONS_ENABLED=true
MLISA_FEATURE_ANALYTICS_ENABLED=true

# =====================================================
# Logging Configuration
# =====================================================
LOG_LEVEL=INFO
SUPERSET_LOG_VIEW=True

# =====================================================
# Celery Configuration
# =====================================================
CELERY_BROKER_URL=redis://:changeme@redis:6379/3
CELERY_RESULT_BACKEND=redis://:changeme@redis:6379/4

# Celery worker configuration
CELERYD_CONCURRENCY=4
CELERYD_MAX_TASKS_PER_CHILD=100

# =====================================================
# Application Configuration
# =====================================================
# Set to your domain
SUPERSET_WEBSERVER_ADDRESS=0.0.0.0
SUPERSET_WEBSERVER_PORT=8088

# Upload limits
UPLOAD_FOLDER=/app/superset_home/uploads
CSV_UPLOAD_MAX_SIZE=100*1024*1024  # 100MB
EXCEL_UPLOAD_MAX_SIZE=100*1024*1024  # 100MB

# =====================================================
# Additional Security Headers
# =====================================================
TALISMAN_ENABLED=True
CONTENT_SECURITY_POLICY_WARNING=False

# =====================================================
# Example production overrides (uncomment as needed)
# =====================================================
# Use external databases for examples
# EXAMPLES_USER=examples_user
# EXAMPLES_PASSWORD=examples_password
# EXAMPLES_HOST=examples-db.your-domain.com
# EXAMPLES_PORT=5432
# EXAMPLES_DB=examples

# OAuth Configuration (uncomment and configure if using OAuth)
# AUTH_TYPE=AUTH_OAUTH
# OAUTH_PROVIDERS=[...]

# LDAP Configuration (uncomment and configure if using LDAP)
# AUTH_TYPE=AUTH_LDAP
# AUTH_LDAP_SERVER=ldap://your-ldap-server.com

# Custom roles and permissions
# AUTH_ROLE_ADMIN=Admin
# AUTH_ROLE_PUBLIC=Public
# AUTH_USER_REGISTRATION=False