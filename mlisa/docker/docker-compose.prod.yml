version: '3.8'

x-superset-image: &superset-image mlisa-data-studio
x-superset-user: &superset-user superset
x-superset-depends-on: &superset-depends-on
  - db
  - redis

services:
  redis:
    image: redis:7
    container_name: superset_cache
    restart: unless-stopped
    volumes:
      - redis:/data

  db:
    image: postgres:15
    container_name: superset_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: superset
      POSTGRES_USER: superset
      POSTGRES_PASSWORD: superset
    volumes:
      - db_home:/var/lib/postgresql/data

  superset:
    build:
      context: ../..
      dockerfile: mlisa/docker/Dockerfile
    env_file:
      - ./.env-mlisa-prod
    image: *superset-image
    container_name: superset_app
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "app-gunicorn"]
    restart: unless-stopped
    ports:
      - 8088:8088
    user: *superset-user
    depends_on: *superset-depends-on
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      SUPERSET_ENV: "production"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/health"]
      interval: 30s
      timeout: 30s
      retries: 3

  superset-init:
    image: *superset-image
    container_name: superset_init
    command: ["/app/scripts/docker-init-mlisa.sh"]
    env_file:
      - ./.env-mlisa-prod
    depends_on: *superset-depends-on
    user: *superset-user
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      SUPERSET_ENV: "production"
    healthcheck:
      disable: true

  superset-worker:
    image: *superset-image
    container_name: superset_worker
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "worker"]
    env_file:
      - ./.env-mlisa-prod
    restart: unless-stopped
    depends_on: *superset-depends-on
    user: *superset-user
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      SUPERSET_ENV: "production"
    healthcheck:
      test: ["CMD-SHELL", "celery -A superset.tasks.celery_app:app inspect ping"]

  superset-worker-beat:
    image: *superset-image
    container_name: superset_worker_beat
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "beat"]
    env_file:
      - ./.env-mlisa-prod
    restart: unless-stopped
    depends_on: *superset-depends-on
    user: *superset-user
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      SUPERSET_ENV: "production"

volumes:
  superset_home:
    external: false
  db_home:
    external: false
  redis:
    external: false
