# --------------------------------------------------------------------
# MLISA Data Studio – Production Build
#   • builds from local Superset source with production optimizations
#   • adds MLISA customizations on top
#   • creates lean final image with no development dependencies
#   • optimized for production deployment
# --------------------------------------------------------------------

ARG PY_VER=3.10-slim-bookworm

######################################################################
# Build frontend assets
######################################################################
FROM node:18-bullseye-slim AS superset-node

WORKDIR /app/superset-frontend

# Set production environment and optimizations
ENV NODE_ENV=production \
    NODE_OPTIONS="--max_old_space_size=6144" \
    NPM_CONFIG_AUDIT=false \
    NPM_CONFIG_FUND=false \
    NPM_CONFIG_UPDATE_NOTIFIER=false

# Install dependencies
COPY superset-frontend/package*.json ./
RUN npm ci --only=production --prefer-offline --no-audit --no-fund && \
    echo "Production npm dependencies installed"

# Build frontend assets
COPY superset-frontend .
RUN npm run build && \
    echo "Production frontend build completed" && \
    ls -la dist/

######################################################################
# Python build stage
######################################################################
FROM python:${PY_VER} AS python-build

WORKDIR /app

# Install system build dependencies
RUN apt-get update -qq && apt-get install -yqq --no-install-recommends \
        build-essential \
        default-libmysqlclient-dev \
        libsasl2-dev \
        libpq-dev \
        libecpg-dev \
        libldap2-dev \
        git \
    && rm -rf /var/lib/apt/lists/*

# Copy Python requirements and install dependencies
COPY requirements/base.txt requirements/
COPY mlisa/requirements.txt mlisa/
RUN pip install --no-cache-dir --upgrade pip setuptools && \
    pip install --no-cache-dir -r requirements/base.txt && \
    pip install --no-cache-dir -r mlisa/requirements.txt && \
    echo "Python dependencies installed"

# Copy Superset source and install
COPY pyproject.toml setup.py MANIFEST.in README.md ./
COPY superset-frontend/package.json superset-frontend/
COPY superset superset
RUN pip install --no-cache-dir -e .

######################################################################
# Final lean production image
######################################################################
FROM python:${PY_VER} AS production

WORKDIR /app

# Production environment variables
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    SUPERSET_ENV=production \
    FLASK_APP="superset.app:create_app()" \
    PYTHONPATH="/app/pythonpath:/app/mlisa" \
    SUPERSET_HOME="/app/superset_home" \
    SUPERSET_PORT=8088 \
    NODE_ENV=production

# Create necessary directories and superset user
RUN mkdir -p ${PYTHONPATH} superset/static requirements superset-frontend apache_superset.egg-info scripts \
    && useradd --user-group -d ${SUPERSET_HOME} -m --no-log-init --shell /bin/bash superset

# Install only runtime dependencies
RUN apt-get update -qq && apt-get install -yqq --no-install-recommends \
        curl \
        default-libmysqlclient-dev \
        libsasl2-dev \
        libsasl2-modules-gssapi-mit \
        libpq-dev \
        libecpg-dev \
        libldap2-dev \
        gosu \
    && touch superset/static/version_info.json \
    && rm -rf /var/lib/apt/lists/*

# Copy Python environment from build stage
COPY --from=python-build /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=python-build /usr/local/bin /usr/local/bin

# Copy frontend assets
COPY --chown=superset:superset --from=superset-node /app/superset-frontend/dist superset/static/assets

# Copy Superset source code
COPY --chown=superset:superset superset superset
COPY --chown=superset:superset pyproject.toml setup.py MANIFEST.in README.md ./

# Copy MLISA customizations
COPY --chown=superset:superset mlisa mlisa
COPY --chown=superset:superset scripts scripts

# Copy docker scripts
COPY --chown=superset:superset mlisa/docker/docker-bootstrap-mlisa.sh scripts/
RUN chmod +x scripts/docker-bootstrap-mlisa.sh

# Set proper ownership
RUN chown -R superset:superset ${SUPERSET_HOME} ${PYTHONPATH} superset mlisa scripts && \
    chmod -R 755 scripts/

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f "http://localhost:${SUPERSET_PORT}/health" || exit 1

# Production security: run as non-root user
USER superset

EXPOSE ${SUPERSET_PORT}

# Default production command with gunicorn
CMD ["/app/scripts/docker-bootstrap-mlisa.sh", "app-gunicorn"]