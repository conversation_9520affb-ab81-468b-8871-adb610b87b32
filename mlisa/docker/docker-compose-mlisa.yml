# This file was moved from project root to docker/ for better organisation.
# MLISA Data Studio Docker Compose Configuration
# (Content identical to previous root-level docker-compose-mlisa.yml)

x-superset-image: &superset-image rsa-mlisa-data-studio
x-superset-user: &superset-user root
x-superset-depends-on: &superset-depends-on
  - db
  - redis
x-superset-volumes: &superset-volumes
  - ../../docker:/app/docker
  - ../../superset:/app/superset
  - ../../mlisa:/app/mlisa
  - ../../superset-frontend:/app/superset-frontend
  - superset_home:/app/superset_home
  - ../../tests:/app/tests

services:
  redis:
    image: redis:7
    container_name: superset_cache
    restart: unless-stopped
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis:/data

  db:
    image: postgres:15
    container_name: superset_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: superset
      POSTGRES_USER: superset
      POSTGRES_PASSWORD: superset
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - db_home:/var/lib/postgresql/data

  superset:
    build:
      context: ../..
      dockerfile: mlisa/docker/Dockerfile.mlisa
    env_file:
      - ./.env-mlisa
    image: *superset-image
    container_name: superset_app
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "app"]
    restart: unless-stopped
    ports:
      - 8088:8088
    extra_hosts:
      - "host.docker.internal:host-gateway"
    user: *superset-user
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"
      HOSTNAME: "superset-app"
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"

  superset-websocket:
    container_name: superset_websocket
    build: ../../superset-websocket
    ports:
      - 8080:8080
    depends_on:
      - redis
    volumes:
      - ../../superset-websocket:/home/<USER>
      - /home/<USER>/node_modules
      - /home/<USER>/dist
      - ../../superset-websocket/config.json:/home/<USER>/config.json
    environment:
      - PORT=8080
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_SSL=false

  superset-init:
    image: *superset-image
    container_name: superset_init
    command: ["/app/scripts/docker-init-mlisa.sh"]
    env_file:
      - ./.env-mlisa
    depends_on: *superset-depends-on
    user: *superset-user
    volumes: *superset-volumes
    environment:
      CYPRESS_CONFIG: "${CYPRESS_CONFIG:-}"
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"
    healthcheck:
      disable: true

  superset-node:
    image: node:18
    container_name: superset_node
    command: ["/app/docker/docker-frontend.sh"]
    env_file:
      - ./.env-mlisa
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      SCARF_ANALYTICS: "${SCARF_ANALYTICS:-}"

  superset-worker:
    image: *superset-image
    container_name: superset_worker
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "worker"]
    env_file:
      - ./.env-mlisa
    restart: unless-stopped
    depends_on: *superset-depends-on
    user: *superset-user
    volumes: *superset-volumes
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      HOSTNAME: "superset-worker"
      CELERYD_CONCURRENCY: 2
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"
    healthcheck:
      test: ["CMD-SHELL", "celery -A superset.tasks.celery_app:app inspect ping -d celery@$$HOSTNAME"]

  superset-worker-beat:
    image: *superset-image
    container_name: superset_worker_beat
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "beat"]
    env_file:
      - ./.env-mlisa
    ports:
      - 5555:5555
    restart: unless-stopped
    depends_on: *superset-depends-on
    user: *superset-user
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"

volumes:
  superset_home:
    external: false
  db_home:
    external: false
  redis:
    external: false
