#!/bin/bash
#
# MLISA Data Studio Bootstrap Script
# Copyright (c) 2024 Ruckus Wireless, Inc., a CommScope Company
# All rights reserved.
#

set -eo pipefail

# If Cypress run – overwrite the password for admin and export env variables
if [ "$CYPRESS_CONFIG" == "true" ]; then
    export SUPERSET_CONFIG=tests.integration_tests.superset_test_config
    export SUPERSET_TESTENV=true
    export ENABLE_REACT_CRUD_VIEWS=true
    export SUPERSET__SQLALCHEMY_DATABASE_URI=postgresql+psycopg2://superset:superset@db:5432/superset
fi

# Make sure we have dev requirements installed
REQUIREMENTS_LOCAL="/app/docker/requirements-local.txt"
if [ -f "${REQUIREMENTS_LOCAL}" ]; then
  echo "Installing local overrides at ${REQUIREMENTS_LOCAL}"
  pip install -r "${REQUIREMENTS_LOCAL}"
else
  echo "Skipping local overrides"
fi

echo "DEPLOYMENT_MODE >>> $DEPLOYMENT_MODE"
echo "SUPERSET_CONFIG_PATH >>> $SUPERSET_CONFIG_PATH"
echo "PYTHONPATH >>> $PYTHONPATH"
echo "FLASK_APP >>> $FLASK_APP"

# Start the appropriate service based on the argument
if [[ "${1}" == "worker" ]]; then
  echo "Starting Celery worker..."
  celery --app=superset.tasks.celery_app:app worker --concurrency=4 -Ofair -l INFO
elif [[ "${1}" == "beat" ]]; then
  echo "Starting Celery flower..."
  celery --app=superset.tasks.celery_app:app flower -l INFO &
  echo "Starting Celery beat..."
  celery --app=superset.tasks.celery_app:app beat --pidfile /tmp/celerybeat.pid -l INFO -s "${SUPERSET_HOME}"/celerybeat-schedule
elif [[ "${1}" == "app" ]]; then
  echo "Starting web app in development mode..."
  # Test if we can import the configuration
  python -c "import mlisa.config; print('Configuration loaded successfully')"
  flask run -p 8088 --with-threads --reload --debugger --host=0.0.0.0
elif [[ "${1}" == "app-gunicorn" ]]; then
  # Apply URL patches based on deployment mode
  python /app/mlisa/patch_urls/replace_url.py $DEPLOYMENT_MODE
  echo "Starting web app in production mode..."
  /usr/bin/run-server.sh
else
  echo "No valid service specified. Please use: worker, beat, app, or app-gunicorn"
  exit 1
fi
