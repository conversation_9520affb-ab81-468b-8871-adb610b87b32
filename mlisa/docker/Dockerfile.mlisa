# --------------------------------------------------------------------
# MLISA Data Studio – built from local Superset source
#   • builds Superset from local source code
#   • adds MLISA customizations on top
#   • installs extra Python dependencies declared in mlisa/requirements.txt
#   • wires Superset to use mlisa.config
# --------------------------------------------------------------------

# Build frontend assets
FROM node:18-bullseye-slim AS superset-node

WORKDIR /app/superset-frontend

# Set memory limits and optimizations for Node.js
ENV NODE_OPTIONS="--max_old_space_size=4096 --max_semi_space_size=128" \
    NPM_CONFIG_PROGRESS=false \
    NPM_CONFIG_AUDIT=false \
    NPM_CONFIG_FUND=false

# Install dependencies
COPY superset-frontend/package.json superset-frontend/package-lock.json ./
RUN npm ci --prefer-offline --no-audit --no-fund --progress=false && \
    echo "npm ci completed"

# Build frontend with timeout and optimizations
COPY superset-frontend .
RUN timeout 1800 npm run build -- --no-color || \
    (echo "Build timed out or failed, trying with reduced parallelism..." && \
      NODE_OPTIONS="--max_old_space_size=3072" npm run build -- --no-color) && \
    echo "npm build completed" && \
    ls -la dist/ && \
    echo "Frontend build completed"

# Main image
FROM python:3.10-slim-bookworm

WORKDIR /app
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    SUPERSET_ENV=production \
    FLASK_APP="superset.app:create_app()" \
    PYTHONPATH="/app/pythonpath" \
    SUPERSET_HOME="/app/superset_home" \
    SUPERSET_PORT=8088

RUN mkdir -p ${PYTHONPATH} superset/static requirements superset-frontend apache_superset.egg-info requirements scripts \
    && useradd --user-group -d ${SUPERSET_HOME} -m --no-log-init --shell /bin/bash superset \
    && apt-get update -qq && apt-get install -yqq --no-install-recommends \
        curl \
        default-libmysqlclient-dev \
        libsasl2-dev \
        libsasl2-modules-gssapi-mit \
        libpq-dev \
        libecpg-dev \
        libldap2-dev \
    && touch superset/static/version_info.json \
    && chown -R superset:superset ./* \
    && rm -rf /var/lib/apt/lists/* && \
    echo "System dependencies installed"

# Copy Superset files
COPY --chown=superset:superset pyproject.toml setup.py README.md ./
COPY --chown=superset:superset superset-frontend/package.json superset-frontend/
COPY --chown=superset:superset requirements/base.txt requirements/

# Install Python dependencies
RUN apt-get update -qq && apt-get install -yqq --no-install-recommends \
      build-essential \
    && pip install --no-cache-dir --upgrade setuptools pip \
    && pip install --no-cache-dir -r requirements/base.txt \
    && apt-get autoremove -yqq --purge build-essential \
    && rm -rf /var/lib/apt/lists/* && \
    echo "Python dependencies installed"

# Copy Superset source
COPY --chown=superset:superset superset superset

# Copy built frontend assets
COPY --from=superset-node --chown=superset:superset /app/superset-frontend/dist superset/static/assets
RUN echo "Frontend assets copied"

# Install Superset
RUN pip install --no-cache-dir -e . && \
    echo "Superset installed"

USER root

# Copy MLISA Python package
COPY --chown=superset:superset mlisa /app/mlisa

# Install MLISA as a Python package
RUN cd /app/mlisa && pip install --no-cache-dir -e . && \
    echo "MLISA package installed"

# Optional extra dependencies
COPY --chown=superset:superset mlisa/requirements.txt /tmp/requirements.txt
RUN if [ -s /tmp/requirements.txt ]; then \
        pip install --no-cache-dir -r /tmp/requirements.txt; \
    fi && rm -f /tmp/requirements.txt && \
    echo "Extra dependencies installed"

# Make sure our code is import-able first
ENV PYTHONPATH="/app/mlisa:${PYTHONPATH}"

# Copy bootstrap and init scripts to /app/scripts to avoid volume mount conflicts
COPY --chown=superset:superset mlisa/docker/docker-bootstrap-mlisa.sh /app/scripts/
COPY --chown=superset:superset mlisa/docker/docker-init-mlisa.sh /app/scripts/
RUN chmod +x /app/scripts/docker-bootstrap-mlisa.sh /app/scripts/docker-init-mlisa.sh && \
    echo "Scripts copied and permissions set"

USER superset

# The base image already sets CMD/ENTRYPOINT, nothing to override
# --------------------------------------------------------------------