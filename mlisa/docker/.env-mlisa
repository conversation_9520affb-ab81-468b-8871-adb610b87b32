#
# MLISA Data Studio Configuration
# Copyright (c) 2024 Ruckus Wireless, Inc., a CommScope Company
# All rights reserved.
#
COMPOSE_PROJECT_NAME=superset

# database configurations (do not modify)
DATABASE_DB=superset
DATABASE_HOST=db
DATABASE_PASSWORD=superset
DATABASE_USER=superset

# database engine specific environment variables
# change the below if you prefers another database engine
DATABASE_PORT=5432
DATABASE_DIALECT=postgresql
POSTGRES_DB=superset
POSTGRES_USER=superset
POSTGRES_PASSWORD=superset
#MYSQL_DATABASE=superset
#MYSQL_USER=superset
#MYSQL_PASSWORD=superset
#MYSQL_RANDOM_ROOT_PASSWORD=yes

# Add the mapped in /app/pythonpath_docker which allows devs to override stuff
PYTHONPATH=/app/pythonpath:/app/docker/pythonpath_dev
REDIS_HOST=redis
REDIS_PORT=6379

FLASK_ENV=development
SUPERSET_ENV=development
SUPERSET_LOAD_EXAMPLES=no
CYPRESS_CONFIG=false
SUPERSET_PORT=8088

# [Start, End: Mlisa code] Mlisa envs are configured below
MLISA_RBAC_URL=http://localhost:3000
MLISA_NOTIFICATION_URL=http://localhost:3006
MLISA_SUPERSET_URL=http://superset:8088

DRUID_HOST=host.docker.internal
DRUID_PORT=58082
DEPLOYMENT_MODE=MLISA
FLOWER_PORT=5555

SUPERSET_MAPBOX_API_KEY=pk.eyJ1IjoibWxpc2EiLCJhIjoiY2t3M2U2dTkxMG9tMTJ2cDYxb3QwZG1kciJ9.MQw6_TtQV7whNPrJJPr5Aw

# SMTP Settings
SMTP_HOST=smtp.mailtrap.io
SMTP_USERNAME=a73d4bcde9ccf6
SMTP_PORT=2525
SMTP_PASSWORD=d37e8ec1c897f6
SMTP_FROM=<EMAIL>

# Druid Lookups
SYSTEM_LOOKUP_NAME=sz_name_map
ZONE_LOOKUP_NAME=zone_name_map
AP_GROUP_LOOKUP_NAME=ap_group_name_map
SWITCH_LOOKUP_NAME=switch_name_map
EDGE_LOOKUP_NAME=edge_name_map
EDGE_CLUSTER_LOOKUP_NAME=edge_cluster_name_map
SYSTEM_TENANT_LOOKUP_NAME=sz_tenant_map
TENANT_LOOKUP_NAME=tenant_name_map
SYSTEM_ZONE_LABEL_LOOKUP_NAME=sz_zone_label_map

# Logs
EVENT_LOGS_RETENTION_PERIOD=7 # days
