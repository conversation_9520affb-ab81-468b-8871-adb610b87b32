# MLISA Data Studio Production Configuration
# ⚠️  WARNING: This is for production use only!
# ⚠️  Make sure to:
#     1. Use your own secure passwords and SECRET_KEY
#     2. Use external databases and Redis in production
#     3. Set up proper SSL/TLS termination
#     4. Configure proper backups and monitoring

x-superset-image: &superset-image rsa-mlisa-data-studio-prod
x-superset-depends-on: &superset-depends-on
  - db
  - redis
x-superset-volumes: &superset-volumes
  # Minimal volumes for production - no source code mounting
  - superset_home:/app/superset_home
  - ../../docker:/app/docker:ro

services:
  redis:
    image: redis:7-alpine
    container_name: superset_cache_prod
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-changeme}
    volumes:
      - redis:/data
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  db:
    image: postgres:15-alpine
    container_name: superset_db_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DATABASE_DB:-superset}
      POSTGRES_USER: ${DATABASE_USER:-superset}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-superset}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - db_home:/var/lib/postgresql/data
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USER:-superset}"]
      interval: 10s
      timeout: 5s
      retries: 5

  superset:
    build:
      context: ../..
      dockerfile: mlisa/docker/Dockerfile.mlisa.prod
      target: production
    env_file:
      - ./.env-mlisa-prod
    image: *superset-image
    container_name: superset_app_prod
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "app-gunicorn"]
    restart: unless-stopped
    ports:
      - "8088:8088"
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"
      GUNICORN_BIND: "0.0.0.0:8088"
      GUNICORN_LIMIT_REQUEST_FIELD_SIZE: "8190"
      GUNICORN_LIMIT_REQUEST_LINE: "4094"
      GUNICORN_TIMEOUT: "300"
      GUNICORN_KEEPALIVE: "2"
      GUNICORN_WORKERS: "4"
      GUNICORN_THREADS: "20"
      GUNICORN_WORKER_CLASS: "gthread"
      GUNICORN_WORKER_CONNECTIONS: "1000"
      GUNICORN_MAX_REQUESTS: "1000"
      GUNICORN_MAX_REQUESTS_JITTER: "100"
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/health"]
      interval: 30s
      timeout: 30s
      retries: 3
      start_period: 60s

  superset-init:
    image: *superset-image
    container_name: superset_init_prod
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "init"]
    env_file:
      - ./.env-mlisa-prod
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      disable: true

  superset-worker:
    image: *superset-image
    container_name: superset_worker_prod
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "worker"]
    env_file:
      - ./.env-mlisa-prod
    restart: unless-stopped
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"
      # Celery worker configuration
      CELERY_WORKERS: "4"
      CELERY_WORKER_TYPE: "gevent"
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "celery -A superset.tasks.celery_app:app inspect ping -d celery@$$HOSTNAME"]
      interval: 30s
      timeout: 30s
      retries: 3

  superset-worker-beat:
    image: *superset-image
    container_name: superset_worker_beat_prod
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "beat"]
    env_file:
      - ./.env-mlisa-prod
    restart: unless-stopped
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      PYTHONPATH: "/app/mlisa:/app/pythonpath"
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      disable: true

volumes:
  superset_home:
    external: false
  db_home:
    external: false
  redis:
    external: false