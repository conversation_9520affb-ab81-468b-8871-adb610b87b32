# MLISA Directory Overview

All MLISA customizations are organized in this directory.

## What's Here

- `backend/` - Python customizations (security, models, APIs)
- `frontend/` - React components and UI customizations
- `database/` - Database migrations and schema changes
- `config/` - Configuration files and settings
- `integration/` - How MLISA connects to Superset core

## Current Status

- **Copied from old repo**: backend/, frontend/, dev-ui/, upgrade-strategy/
- **New structure**: database/, config/, integration/ (empty, ready for migration)

## Next Steps

1. Move existing customizations into proper subdirectories
2. Set up integration points with Superset 4.1.2
3. Test and migrate functionality