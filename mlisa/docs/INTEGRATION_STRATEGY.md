# MLISA Integration Strategy

## Overview

This document outlines the clear strategy for integrating MLISA customizations with Apache Superset, ensuring maintainability, upgradeability, and clean separation of concerns.

## Integration Architecture

### 1. Configuration-Based Integration (Primary Method)

**Location**: `mlisa/config.py`

**How it works**:
- Super<PERSON> loads MLISA configuration via `SUPERSET_CONFIG_PATH` environment variable
- Configuration overrides Superset defaults without modifying core code
- MLISA-specific settings are injected into Superset's configuration system

**Example**:
```python
# In mlisa/config.py
from mlisa.backend.security.custom_auth import CustomSecurityManager

CUSTOM_SECURITY_MANAGER = CustomSecurityManager
SQLALCHEMY_DATABASE_URI = f"postgresql://{DATABASE_USER}:{DATABASE_PASSWORD}@{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_DB}"
```

### 2. Python Package Integration

**Location**: `mlisa/` directory structure

**How it works**:
- MLISA code is installed as a Python package during Docker build
- `PYTHON<PERSON>TH` includes MLISA directory for import resolution
- MLISA modules can be imported and used throughout Superset

**Dockerfile setup**:
```dockerfile
# Copy MLISA Python package
COPY --chown=superset:superset mlisa /app/mlisa
ENV PYTHONPATH="/app/mlisa:${PYTHONPATH}"
```

### 3. Security Manager Override

**Location**: `mlisa/backend/security/`

**How it works**:
- Custom security manager extends Superset's `SupersetSecurityManager`
- Replaces default authentication and authorization behavior
- Integrates with MLISA's RBAC system

**Implementation**:
```python
# In config.py
CUSTOM_SECURITY_MANAGER = CustomSecurityManager

# In custom_auth.py
class CustomSecurityManager(SupersetSecurityManager):
    authdbview = CustomAuthDBView
```

## Directory Structure

```
mlisa/
├── backend/                    # Backend customizations
│   ├── security/              # Authentication & authorization
│   │   ├── custom_auth.py     # Custom security manager
│   │   ├── rbac_helper.py     # RBAC operations
│   │   └── permissions.py     # Permission definitions
│   ├── utils/                 # Utility functions
│   │   ├── rbac_adapter.py    # External RBAC API integration
│   │   └── ...                # Other utilities
│   └── api/                   # Custom API endpoints
├── docker/                    # Docker configuration
│   ├── common_config.py       # Main configuration file
│   ├── Dockerfile.mlisa       # MLISA-specific Dockerfile
│   └── docker-compose-mlisa.yml
└── docs/                      # Documentation
```

## Integration Points

### 1. Configuration Injection
- **Entry Point**: `SUPERSET_CONFIG_PATH` environment variable
- **Target**: Superset's configuration system
- **Method**: Direct configuration overrides

### 2. Security Override
- **Entry Point**: `CUSTOM_SECURITY_MANAGER` configuration
- **Target**: Superset's security framework
- **Method**: Class inheritance and method override

### 3. API Extensions
- **Entry Point**: Custom Flask views in `mlisa/backend/api/`
- **Target**: Superset's API endpoints
- **Method**: Flask blueprint registration

### 4. Database Integration
- **Entry Point**: Custom models in `mlisa/backend/models/`
- **Target**: Superset's database schema
- **Method**: SQLAlchemy model extensions

## Best Practices

### 1. Separation of Concerns
- ✅ Keep MLISA code in `mlisa/` directory
- ✅ Don't modify Superset core files
- ✅ Use configuration overrides instead of code changes

### 2. Upgradeability
- ✅ Use inheritance over direct modification
- ✅ Maintain compatibility with Superset APIs
- ✅ Version-specific adaptations when needed

### 3. Testing
- ✅ Test MLISA components independently
- ✅ Integration tests with Superset
- ✅ Mock external dependencies

### 4. Documentation
- ✅ Document all integration points
- ✅ Maintain upgrade guides
- ✅ Keep configuration examples

## Migration Strategy

### From Legacy Integration
1. **Phase 1**: Move code to new structure (✅ Complete)
2. **Phase 2**: Update import paths and references
3. **Phase 3**: Test integration points
4. **Phase 4**: Remove unused legacy code

### For Future Upgrades
1. **Assessment**: Review Superset changes
2. **Compatibility**: Check MLISA integration points
3. **Adaptation**: Update MLISA code if needed
4. **Testing**: Validate functionality
5. **Deployment**: Roll out changes

## Monitoring and Maintenance

### Configuration Validation
- Validate all MLISA configuration settings
- Check for deprecated Superset settings
- Monitor for configuration conflicts

### Security Auditing
- Regular review of custom security implementations
- Validate RBAC integration
- Check for security vulnerabilities

### Performance Monitoring
- Monitor MLISA-specific performance impacts
- Track database query performance
- Monitor external API calls

## Conclusion

This integration strategy provides:
- **Clean separation** between MLISA and Superset code
- **Easy upgrades** with minimal disruption
- **Maintainable codebase** with clear boundaries
- **Scalable architecture** for future enhancements

The key is using Superset's built-in extension points rather than modifying core code, ensuring long-term maintainability and upgradeability.