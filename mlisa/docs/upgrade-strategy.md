# MLISA Superset Upgrade Strategy

## Overview

This directory contains the strategy and documentation for upgrading from Superset 1.5.1 to 4.1.2 while maintaining production stability.

## Dual-Track Approach

### Track 1: Production Maintenance (Current Repository)
- **Repository**: `rsa-superset` (this repository)
- **Purpose**: Continue production releases and critical fixes
- **Version**: 1.5.1-mlisa with maintenance releases
- **Timeline**: Next 6 months

### Track 2: Upgrade Development (New Repository)
- **Repository**: `mlisa-data-studio` (this repository)
- **Purpose**: Upgrade to Superset 4.1.2 with MLISA customizations
- **Timeline**: 6-month development cycle

## Current MLISA Customizations

### Database Schema Changes
- `report_schedule` table: Added `metadata_json` and `uuid` columns
- `ab_user`/`ab_role` tables: Increased column lengths from 64 to 128 chars
- `logs` table: Added performance indexes

### Frontend Package
- `@superset-ui/mlisa` package in `packages/superset-ui-mlisa/`
- Custom UI components and controls
- Integrated with main Superset build process

### Backend Customizations
- Custom API endpoints
- Security manager modifications
- MLISA-specific business logic

## Next Steps

1. **Phase 1**: Set up clean MLISA organization (✓ Done)
2. **Phase 2**: Copy existing customizations to new structure
3. **Phase 3**: Set up Superset 4.1.2 base
4. **Phase 4**: Migrate and test customizations
5. **Phase 5**: Production deployment preparation

This approach ensures zero disruption to production while systematically preparing for the upgrade.