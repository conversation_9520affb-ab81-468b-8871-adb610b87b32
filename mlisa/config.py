# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

# =============================================================================
# MLISA MAIN CONFIGURATION
# Environment-aware configuration loader
# =============================================================================

import os

# Determine which environment we're running in
SUPERSET_ENV = os.environ.get("SUPERSET_ENV", "development").lower()
FLASK_ENV = os.environ.get("FLASK_ENV", "development").lower()

# Use SUPERSET_ENV as primary, FLASK_ENV as fallback
ENVIRONMENT = SUPERSET_ENV if SUPERSET_ENV in ["development", "production"] else FLASK_ENV

print(f"🌍 Loading MLISA configuration for: {ENVIRONMENT.upper()}")

# Import the appropriate environment configuration
if ENVIRONMENT == "development":
    print("📂 Loading development configuration...")
    from mlisa.config.environments.development import *
elif ENVIRONMENT == "production":
    print("📂 Loading production configuration...")
    from mlisa.config.environments.production import *
else:
    # Default to development for unknown environments
    print(f"⚠️  Unknown environment '{ENVIRONMENT}', defaulting to development")
    from mlisa.config.environments.development import *

print("✅ Configuration loaded successfully!")
