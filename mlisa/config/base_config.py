# MLISA Base Configuration
# Common settings shared across all environments

import os
from datetime import datetime, timedelta
from typing import Optional

from celery.schedules import crontab


def get_env_variable(var_name: str, default: Optional[str] = None) -> str:
    """Get environment variable or return default/raise error if not found."""
    try:
        return os.environ[var_name]
    except KeyError:
        if default is not None:
            return default
        else:
            error_msg = f"The environment variable {var_name} was missing, abort..."
            raise EnvironmentError(error_msg)


# =============================================================================
# CORE ENVIRONMENT DETECTION
# =============================================================================
IS_DEVELOPMENT = (
    os.environ.get("FLASK_ENV", "").lower() == "development"
    or os.environ.get("SUPERSET_ENV", "").lower() == "development"
)

# =============================================================================
# BASIC SETTINGS (SHARED ACROSS ENVIRONMENTS)
# =============================================================================

# Deployment mode with fallback
DEPLOYMENT_MODE = get_env_variable("DEPLOYMENT_MODE", "MLISA")

# Database Configuration (common structure) with fallbacks
DATABASE_DIALECT = get_env_variable("DATABASE_DIALECT", "postgresql")
DATABASE_USER = get_env_variable("DATABASE_USER", "superset")
DATABASE_PASSWORD = get_env_variable("DATABASE_PASSWORD", "superset")
DATABASE_PORT = get_env_variable("DATABASE_PORT", "5432")
DATABASE_DB = get_env_variable("DATABASE_DB", "superset")

# Druid Configuration with fallbacks
DRUID_HOST = get_env_variable("DRUID_HOST", "localhost")
DRUID_PORT = get_env_variable("DRUID_PORT", "8082")
DRUID_BROKER_SERVICE = get_env_variable("DRUID_BROKER_SERVICE", "druid:broker-adhoc")

# URL Prefix based on deployment mode
URL_PREFIX = "/analytics/explorer/" if DEPLOYMENT_MODE == "MLISA" else "/api/a4rc/explorer/"

# Redis Configuration (defaults will be overridden in environment-specific configs)
REDIS_CELERY_DB = get_env_variable("REDIS_CELERY_DB", "0")
REDIS_RESULTS_DB = get_env_variable("REDIS_RESULTS_DB", "1")
REDIS_RLS_DB = get_env_variable("REDIS_RLS_DB", "2")

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================
CELERY_IMPORTS = ("superset.sql_lab", "superset.tasks", "superset.tasks.thumbnails")
CELERYD_LOG_LEVEL = "DEBUG"
CELERYD_PREFETCH_MULTIPLIER = 1
CELERY_ACKS_LATE = False
CELERYBEAT_SCHEDULE = {
    "reports.scheduler": {
        "task": "reports.scheduler",
        "schedule": crontab(minute="*", hour="*"),  # every minute
    },
    "reports.prune_log": {
        "task": "reports.prune_log",
        "schedule": crontab(minute=10, hour=0),  # 10 past 12 AM UTC
    },
    "events.prune_log": {
        "task": "events.prune_log",
        "schedule": crontab(minute=0, hour=2),  # 2 AM UTC
    },
}

# =============================================================================
# FEATURE FLAGS (SHARED)
# =============================================================================
FEATURE_FLAGS = {
    "ALERT_REPORTS": True,
    "EMBEDDED_SUPERSET": True,
    "THUMBNAILS": False,
    "ROW_LEVEL_SECURITY": False,
    "THUMBNAILS_SQLA_LISTENERS": False,
    "ENABLE_EXPLORE_DRAG_AND_DROP": True,
    "ENABLE_DND_WITH_CLICK_UX": True,
    "DASHBOARD_NATIVE_FILTERS": True,
    "UX_BETA": True,
    "BOUNCED_EMAIL_CHECK": False,
}

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_FORMAT = "[%(asctime)s] %(levelname)s | %(module)s | %(message)s"
LOG_DATE_FORMAT = "%B %d, %Y %H:%M:%S.%03d %Z"

# =============================================================================
# CACHE CONFIGURATION (COMMON STRUCTURE)
# =============================================================================
CACHE_TYPE = "redis"
CACHE_KEY_PREFIX = "superset_results"

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
PERMANENT_SESSION_LIFETIME = timedelta(days=7) if DEPLOYMENT_MODE == "MLISA" else timedelta(hours=1)
SESSION_COOKIE_NAME = "ds_session"

# =============================================================================
# WEBDRIVER CONFIGURATION
# =============================================================================
WEBDRIVER_TYPE = "firefox"
SCREENSHOT_LOCATE_WAIT = 100
SCREENSHOT_LOAD_WAIT = 600

# =============================================================================
# SECURITY & AUTH
# =============================================================================
GUEST_ROLE_NAME = "Guest"
WTF_CSRF_ENABLED = False  # May be overridden in production

# =============================================================================
# PDF REPORT CONFIGURATION
# =============================================================================
PDF_PAGE_SIZE = (1300, 1900)
FOOTER_TEXT = "© %s Ruckus Wireless, Inc., a CommScope Company. All rights reserved." % datetime.now().year
PDF_FIRST_PAGE_TEMPLATE_PATH = "/app/superset/mlisa/assets/images/pdf_first_page_template.png"
RUCKUS_LOGO_WHITE_PATH = "/app/superset/mlisa/assets/images/ruckus_logo_white.png"
RUCKUS_LOGO_BLACK_PATH = "/app/superset/mlisa/assets/images/ruckus_logo_black.png"
PDF_FONT_TEMPLATE_PATH = "/app/superset/mlisa/assets/fonts/montserrat_bold.ttf"
PDF_TIME_RANGE_FONT_TEMPLATE_PATH = "/app/superset/mlisa/assets/fonts/opensans_regular.ttf"
PDF_WEBDRIVER_WINDOW = {"dashboard": (1600, 2000)}
PDF_RUCKUS_BRANDING = False

# =============================================================================
# APPLICATION BRANDING
# =============================================================================
APP_NAME = ""
APP_ICON = ""
APP_ICON_WIDTH = 0

# =============================================================================
# SECRET KEYS
# =============================================================================
SECRET_KEY = "zXmHU6EKDDaJxLzo9TOzHir+csW8Wo+oF3EHk4YiPeZX3X0g89dPsEUN"
PREVIOUS_SECRET_KEY = "21thisismyscretkey12eyyh"
RUCKUS_ADMIN_SECRET = "H@9j#2Lg5$7rT@1!mZp0"

# =============================================================================
# MAPBOX
# =============================================================================
MAPBOX_API_KEY = get_env_variable("SUPERSET_MAPBOX_API_KEY", "")

# =============================================================================
# GALLERY CONFIGURATION
# =============================================================================
PREBUILT_DASHBOARDS_PATH = f"superset/mlisa/verticals/{DEPLOYMENT_MODE.lower()}"
GALLERY_METADATA_PATH = f"superset/mlisa/gallery/{DEPLOYMENT_MODE.lower()}"

# =============================================================================
# PERFORMANCE & TIMEOUT SETTINGS (DEFAULTS - MAY BE OVERRIDDEN)
# =============================================================================
SUPERSET_WEBSERVER_TIMEOUT = 300
SQLLAB_VALIDATION_TIMEOUT = 300
SQLLAB_QUERY_COST_ESTIMATE_TIMEOUT = 300

# =============================================================================
# DATABASE-SPECIFIC CONFIGURATION
# =============================================================================
if DATABASE_DIALECT == "postgresql":
    SQLALCHEMY_POOL_SIZE = 10
    SQLALCHEMY_POOL_TIMEOUT = 300
    SQLALCHEMY_MAX_OVERFLOW = 30

# =============================================================================
# EMAIL CONFIGURATION (DEFAULTS - MAY BE OVERRIDDEN)
# =============================================================================
EMAIL_REPORTS_SUBJECT_PREFIX = "Scheduled Report:"
EVENT_LOG_CLEANUP_DAYS = get_env_variable("EVENT_LOGS_RETENTION_PERIOD", "90")

# =============================================================================
# SPLIT.IO CONFIGURATION
# =============================================================================
SPLIT_IO_CONFIG = {
    "ALTO": {"proxy_url": "https://splitproxy.dev.ruckus.cloud/api", "api_key": "m5a0p0vc4rpbqt4cndfjflt7vfcn44eo691e"},
    "MLISA": {"proxy_url": "", "api_key": "95ges1j8teniel9sf757lh3el2dber8qubq4"},
}

# =============================================================================
# CUSTOM SECURITY MANAGER
# =============================================================================
try:
    from mlisa.backend.security.custom_auth import CustomSecurityManager

    CUSTOM_SECURITY_MANAGER = CustomSecurityManager
except ImportError:
    print("⚠️  MLISA package not available, using default Superset security manager")
    CUSTOM_SECURITY_MANAGER = None

print(f"📋 Base configuration loaded for {DEPLOYMENT_MODE} deployment")
