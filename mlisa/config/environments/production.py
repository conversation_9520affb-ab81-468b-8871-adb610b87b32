# Production Environment Configuration
# This file contains settings specific to production environment

from ..base_config import *

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================

# Security and CORS settings (restrictive)
DEBUG = False
ENABLE_CORS = False
CORS_OPTIONS = {}  # No CORS allowed in production for security

# =============================================================================
# PRODUCTION DATABASE & INFRASTRUCTURE
# =============================================================================
DATABASE_HOST = get_env_variable("DATABASE_HOST")
REDIS_HOST = get_env_variable("REDIS_HOST")
REDIS_PORT = get_env_variable("REDIS_PORT")

# Database URI for production
SQLALCHEMY_DATABASE_URI = f"postgresql://{DATABASE_USER}:{DATABASE_PASSWORD}@{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_DB}"

# =============================================================================
# PRODUCTION CACHE & PERFORMANCE
# =============================================================================
CACHE_DEFAULT_TIMEOUT = 86400  # 24 hours - long cache for production
CACHE_REDIS_HOST = REDIS_HOST
CACHE_REDIS_PORT = REDIS_PORT
CACHE_REDIS_DB = REDIS_RESULTS_DB

# Production timeouts (optimized for performance)
SQLLAB_TIMEOUT = 300  # 5 minutes max
REQUEST_LOG_SAMPLING_RATE = 0.1  # Sample only 10% of requests

# =============================================================================
# PRODUCTION EXTERNAL SERVICES
# =============================================================================
MLISA_SUPERSET_URL = get_env_variable("MLISA_SUPERSET_URL")
MLISA_RBAC_URL = get_env_variable("MLISA_RBAC_URL")

# WebDriver URLs for production
WEBDRIVER_BASEURL = f"{MLISA_SUPERSET_URL}{URL_PREFIX}"
WEBDRIVER_BASEURL_USER_FRIENDLY = WEBDRIVER_BASEURL
LOGO_TARGET_PATH = URL_PREFIX

# =============================================================================
# PRODUCTION EMAIL & NOTIFICATIONS
# =============================================================================
EMAIL_NOTIFICATIONS = True
SMTP_HOST = get_env_variable("SMTP_HOST")
SMTP_PORT = get_env_variable("SMTP_PORT")
SMTP_USER = get_env_variable("SMTP_USERNAME")
SMTP_PASSWORD = get_env_variable("SMTP_PASSWORD")
SMTP_MAIL_FROM = get_env_variable("SMTP_FROM")

# =============================================================================
# PRODUCTION SPLIT.IO
# =============================================================================
def get_split_io_config():
    """Production Split.io configuration - always use environment variables."""
    return {
        "proxy_url": get_env_variable("SPLIT_PROXY_ENDPOINT", ""),
        "api_key": get_env_variable("SPLIT_IO_KEY", "")
    }

split_config = get_split_io_config()
SPLIT_IO_PROXY_URL = split_config["proxy_url"]
SPLIT_IO_KEY = split_config["api_key"]

# =============================================================================
# PRODUCTION SECURITY (STRICT)
# =============================================================================
WTF_CSRF_ENABLED = True
SESSION_COOKIE_SECURE = True  # Require HTTPS
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = "Strict"  # Strict same-site policy

# Rate limiting enabled in production
RATELIMIT_ENABLED = True

print("🚀 Production configuration loaded")
print(f"   ├── Database: {DATABASE_HOST}")
print(f"   ├── Redis: {REDIS_HOST}:{REDIS_PORT}")
print(f"   ├── CORS: Disabled for security")
print(f"   ├── CSRF: Enabled")
print(f"   └── Debug mode: {DEBUG}")