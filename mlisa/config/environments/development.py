# Development Environment Configuration
# This file contains settings specific to development environment

from ..base_config import *

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================

# Debug and CORS settings
DEBUG = True
ENABLE_CORS = True
CORS_OPTIONS = {
    "supports_credentials": True,
    "allow_headers": ["*"],
    "resources": ["*"],
    "origins": ["http://localhost:5173", "http://localhost:3000"],
}

# =============================================================================
# DEVELOPMENT DATABASE & INFRASTRUCTURE
# =============================================================================
# Use environment variables if available, fallback to localhost for local dev
DATABASE_HOST = get_env_variable("DATABASE_HOST", "localhost")
REDIS_HOST = get_env_variable("REDIS_HOST", "localhost")
REDIS_PORT = get_env_variable("REDIS_PORT", "6379")

# Database URI for development
SQLALCHEMY_DATABASE_URI = f"postgresql://{DATABASE_USER}:{DATABASE_PASSWORD}@{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_DB}"

# =============================================================================
# DEVELOPMENT CACHE & PERFORMANCE
# =============================================================================
CACHE_DEFAULT_TIMEOUT = 300  # 5 minutes - short for development
CACHE_REDIS_HOST = REDIS_HOST
CACHE_REDIS_PORT = REDIS_PORT
CACHE_REDIS_DB = REDIS_RESULTS_DB

# Development timeouts (longer for debugging)
SQLLAB_TIMEOUT = 600  # 10 minutes for debugging queries
REQUEST_LOG_SAMPLING_RATE = 1.0  # Log 100% of requests in dev

# =============================================================================
# DEVELOPMENT EXTERNAL SERVICES
# =============================================================================
MLISA_SUPERSET_URL = get_env_variable("MLISA_SUPERSET_URL", "http://localhost:8088")
MLISA_RBAC_URL = "http://localhost:3001/api"

# WebDriver URLs for development
WEBDRIVER_BASEURL = f"{MLISA_SUPERSET_URL}{URL_PREFIX}"
WEBDRIVER_BASEURL_USER_FRIENDLY = WEBDRIVER_BASEURL
LOGO_TARGET_PATH = URL_PREFIX

# =============================================================================
# DEVELOPMENT EMAIL & NOTIFICATIONS
# =============================================================================
EMAIL_NOTIFICATIONS = True  # Enable for testing
SMTP_HOST = "localhost"
SMTP_PORT = "1025"  # Mailhog or similar for local email testing
SMTP_USER = "dev@localhost"
SMTP_PASSWORD = "dev"
SMTP_MAIL_FROM = "superset-dev@localhost"

# =============================================================================
# DEVELOPMENT SPLIT.IO
# =============================================================================
def get_split_io_config():
    """Development Split.io configuration with auto-selection."""
    mode_config = SPLIT_IO_CONFIG.get(DEPLOYMENT_MODE, {})
    return {
        "proxy_url": mode_config.get("proxy_url", ""),
        "api_key": mode_config.get("api_key", "")
    }

split_config = get_split_io_config()
SPLIT_IO_PROXY_URL = split_config["proxy_url"]
SPLIT_IO_KEY = split_config["api_key"]

# =============================================================================
# DEVELOPMENT SECURITY (RELAXED)
# =============================================================================
WTF_CSRF_ENABLED = False
SESSION_COOKIE_SECURE = False  # Allow non-HTTPS in dev
SESSION_COOKIE_HTTPONLY = True

print("🛠️  Development configuration loaded")
print(f"   ├── Database: {DATABASE_HOST}")
print(f"   ├── Redis: {REDIS_HOST}:{REDIS_PORT}")
print(f"   ├── CORS: Enabled for localhost:5173")
print(f"   └── Debug mode: {DEBUG}")