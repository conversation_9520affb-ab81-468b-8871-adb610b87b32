# MLISA Data Studio

MLISA-specific customizations for Apache Superset, providing multi-tenant analytics with RBAC integration.

## Quick Start

### Development
```bash
# Fast development build (18 seconds)
docker-compose -f mlisa/docker/docker-compose-mlisa-fast.yml up

# Full development build with live frontend
docker-compose -f mlisa/docker/docker-compose-mlisa.yml up
```

### Production
```bash
# Build and deploy production image
./scripts/build-mlisa-production.sh
docker-compose -f mlisa/docker/docker-compose-mlisa-prod.yml up -d
```

## Architecture

```
mlisa/
├── config.py                  # Main configuration file
├── backend/                   # Python backend customizations
│   ├── security/              # Authentication & RBAC
│   ├── api/                   # REST API endpoints
│   └── utils/                 # Utility functions
├── frontend/                  # React frontend customizations
├── docker/                    # Docker configurations
│   ├── Dockerfile.mlisa       # Development build
│   ├── Dockerfile.mlisa.fast  # Fast build (pre-built assets)
│   └── Dockerfile.mlisa.prod  # Production build
└── docs/                      # Documentation
```

## Key Features

- **Multi-tenant data isolation** with row-level security
- **Header-based authentication** from external RBAC service
- **Role-based permissions** (admin, network-admin, report-only, guest)
- **Multiple deployment options** (dev, fast, production)
- **External service integration** for authentication and notifications

## Configuration

MLISA integrates with Superset through configuration override:

```python
# mlisa/config.py is loaded via SUPERSET_CONFIG_PATH
CUSTOM_SECURITY_MANAGER = CustomSecurityManager
SQLALCHEMY_DATABASE_URI = f"postgresql://{DATABASE_USER}:..."
```

## Environment Variables

```bash
DEPLOYMENT_MODE=MLISA          # or ALTO
DATABASE_HOST=localhost
DATABASE_USER=superset
MLISA_RBAC_URL=http://rbac-service:3000
```

## Authentication Headers

Expected from upstream RBAC service:
```
x-mlisa-user-id: user123
x-mlisa-user-email: <EMAIL>
x-mlisa-tenant-ids: ["tenant1", "tenant2"]
x-mlisa-is-super-tenant: false
```

## Development

1. **Backend changes**: Modify files in `mlisa/backend/`
2. **Frontend changes**: Modify files in `mlisa/frontend/`
3. **Configuration**: Update `mlisa/config.py`
4. **Docker**: Use fast build for quick iteration

## Documentation

- [Integration Strategy](docs/INTEGRATION_STRATEGY.md) - How MLISA integrates with Superset
- [Build Troubleshooting](mlisa/docker/BUILD_TROUBLESHOOTING.md) - Docker build issues
- [License Headers](LICENSE_HEADER.txt) - Template for MLISA file headers

## Support

For build issues, see troubleshooting guide. For MLISA-specific questions, contact the Data Studio team.