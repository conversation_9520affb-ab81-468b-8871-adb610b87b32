#
# MLISA Data Studio
# Copyright (c) 2024 Ruckus Wireless, Inc., a CommScope Company
# All rights reserved.
#

# Usage instructions:
# - For shell scripts: Use the header above with #!/bin/bash on the first line
# - For Python files: Use the header above with proper Python comment format
# - For configuration files: Use the header above as-is
# - For Docker files: Use the header above with proper Dockerfile comment format

# Examples:

# Shell script header:
#!/bin/bash
#
# MLISA Data Studio [Description]
# Copyright (c) 2024 Ruckus Wireless, Inc., a CommScope Company
# All rights reserved.
#

# Python file header:
#
# MLISA Data Studio [Description]
# Copyright (c) 2024 Ruckus Wireless, Inc., a CommScope Company
# All rights reserved.
#

# Configuration file header:
#
# MLISA Data Studio [Description]
# Copyright (c) 2024 Ruckus Wireless, Inc., a CommScope Company
# All rights reserved.