import os
import logging
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)

# Deployment mode configuration
DEPLOYMENT_MODE = os.environ.get("DEPLOYMENT_MODE", "MLISA")

# Configuration constants
DEFAULT_CONFIG = {
    "CACHE_TIMEOUT": 300,
    "SESSION_TIMEOUT": 3600,
    "MAX_LOGIN_ATTEMPTS": 5,
    "PASSWORD_MIN_LENGTH": 8,
    "JWT_EXPIRATION": 86400,  # 24 hours
}


def get_deployment_mode() -> str:
    """Get the current deployment mode.

    Returns:
        Deployment mode string (MLISA or ALTO)
    """
    mode = os.environ.get("DEPLOYMENT_MODE", "MLISA").upper()
    if mode not in ["MLISA", "ALTO"]:
        logger.warning("Invalid deployment mode %s, defaulting to MLISA", mode)
        return "MLISA"
    return mode


def get_config_value(key: str, default: Any = None) -> Any:
    """Get configuration value from environment or default.

    Args:
        key: Configuration key
        default: Default value if not found

    Returns:
        Configuration value
    """
    # Try environment variable first
    env_value = os.environ.get(key)
    if env_value is not None:
        return env_value

    # Try default configuration
    if key in DEFAULT_CONFIG:
        return DEFAULT_CONFIG[key]

    # Return provided default
    return default


def get_database_config() -> Dict[str, Any]:
    """Get database configuration.

    Returns:
        Database configuration dictionary
    """
    return {
        "host": os.environ.get("DB_HOST", "localhost"),
        "port": int(os.environ.get("DB_PORT", "5432")),
        "database": os.environ.get("DB_NAME", "superset"),
        "username": os.environ.get("DB_USER", "superset"),
        "password": os.environ.get("DB_PASSWORD", "superset"),
        "pool_size": int(os.environ.get("DB_POOL_SIZE", "10")),
        "max_overflow": int(os.environ.get("DB_MAX_OVERFLOW", "20")),
    }


def get_security_config() -> Dict[str, Any]:
    """Get security configuration.

    Returns:
        Security configuration dictionary
    """
    return {
        "secret_key": os.environ.get("SECRET_KEY", "change-me-in-production"),
        "jwt_secret": os.environ.get("JWT_SECRET", "jwt-secret-change-me"),
        "session_timeout": int(get_config_value("SESSION_TIMEOUT", 3600)),
        "max_login_attempts": int(get_config_value("MAX_LOGIN_ATTEMPTS", 5)),
        "password_min_length": int(get_config_value("PASSWORD_MIN_LENGTH", 8)),
        "require_https": os.environ.get("REQUIRE_HTTPS", "false").lower() == "true",
    }


def is_development_mode() -> bool:
    """Check if running in development mode.

    Returns:
        True if in development mode, False otherwise
    """
    return os.environ.get("FLASK_ENV", "production").lower() == "development"


def validate_configuration() -> bool:
    """Validate critical configuration settings.

    Returns:
        True if configuration is valid, False otherwise
    """
    issues = []

    # Check required environment variables in production
    if not is_development_mode():
        required_vars = ["SECRET_KEY", "DB_HOST", "DB_PASSWORD"]
        for var in required_vars:
            if not os.environ.get(var):
                issues.append(f"Missing required environment variable: {var}")

    # Validate deployment mode
    deployment_mode = get_deployment_mode()
    if deployment_mode not in ["MLISA", "ALTO"]:
        issues.append(f"Invalid deployment mode: {deployment_mode}")

    if issues:
        for issue in issues:
            logger.error("Configuration issue: %s", issue)
        return False

    logger.info("Configuration validation passed")
    return True
