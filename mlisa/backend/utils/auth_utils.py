import logging
import re
from typing import Dict, Any, Optional

from mlisa.backend.utils.constants import (
    ALTO_ROLE,
    EMAIL,
    FIRST_NAME,
    HEADER_MLISA_IS_SUPER_TENANT,
    HEADER_MLISA_IS_SUPPORT,
    HEADER_MLISA_OWN_TENANT_ID,
    HEADER_MLISA_PARENT_TENANT_ID,
    HEADER_MLISA_TENANT_IDS,
    HEADER_MLISA_USER_DATA_STUDIO_ROLE,
    HEADER_MLISA_USER_EMAIL,
    HEADER_MLISA_USER_FIRST_NAME,
    HEADER_MLISA_USER_ID,
    HEADER_MLISA_USER_LAST_NAME,
    HEADER_MLISA_USER_LOWEST_ROLE,
    IS_FRANCHISOR,
    LAST_NAME,
    MLISA_ROLE,
    OWN_TENANT_ID,
    PARENT_TENANT_ID,
    TENANT_IDS,
    <PERSON>ER_<PERSON>,
    MAX_<PERSON>ERNAME_LENGTH,
    MAX_EMAIL_LENGTH,
)

logger = logging.getLogger(__name__)


def validate_required_headers(headers: Dict[str, str], user_data: Dict[str, Any], deployment_mode: str) -> Optional[Dict[str, Any]]:
    """Validate required HTTP headers and extract user information.

    Args:
        headers: HTTP headers from the request
        user_data: User data from request payload
        deployment_mode: Deployment mode (MLISA or ALTO)

    Returns:
        Dictionary containing validated user information, or None if validation fails
    """
    try:
        # Extract user information from headers
        user_info = {
            USER_ID: headers.get(HEADER_MLISA_USER_ID),
            FIRST_NAME: headers.get(HEADER_MLISA_USER_FIRST_NAME),
            LAST_NAME: headers.get(HEADER_MLISA_USER_LAST_NAME),
            EMAIL: headers.get(HEADER_MLISA_USER_EMAIL),
            OWN_TENANT_ID: headers.get(HEADER_MLISA_OWN_TENANT_ID),
            PARENT_TENANT_ID: headers.get(HEADER_MLISA_PARENT_TENANT_ID),
            TENANT_IDS: headers.get(HEADER_MLISA_TENANT_IDS),
            IS_FRANCHISOR: headers.get(HEADER_MLISA_IS_SUPER_TENANT, "false"),
        }

        # Add role information based on deployment mode
        if deployment_mode == "MLISA":
            user_info[MLISA_ROLE] = headers.get(HEADER_MLISA_USER_DATA_STUDIO_ROLE)
        else:
            user_info[ALTO_ROLE] = headers.get(HEADER_MLISA_USER_LOWEST_ROLE)

        # Update with user data from payload if provided
        if user_data:
            user_info = update_user_data(headers, user_data)

        # Validate required fields
        required_fields = [USER_ID, EMAIL, FIRST_NAME, LAST_NAME]
        if deployment_mode == "MLISA":
            required_fields.append(MLISA_ROLE)
        else:
            required_fields.append(ALTO_ROLE)

        for field in required_fields:
            if not user_info.get(field):
                logger.error("Missing required field: %s", field)
                return None

        # Validate field formats
        if not _validate_user_info_format(user_info):
            return None

        # Extract names from email if names are missing
        if not user_info.get(FIRST_NAME) or not user_info.get(LAST_NAME):
            extracted_names = extract_names_from_email(user_info.get(EMAIL, ""))
            if extracted_names:
                user_info[FIRST_NAME] = extracted_names.get("first_name", user_info.get(FIRST_NAME))
                user_info[LAST_NAME] = extracted_names.get("last_name", user_info.get(LAST_NAME))

        logger.info("Successfully validated user information for user: %s", user_info.get(USER_ID))
        return user_info

    except Exception as e:
        logger.error("Error validating headers: %s", e)
        return None


def update_user_data(headers: Dict[str, str], user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Update user information with data from request payload.

    Args:
        headers: HTTP headers
        user_data: User data from request payload

    Returns:
        Updated user information dictionary
    """
    user_info = {
        USER_ID: user_data.get("userId") or headers.get(HEADER_MLISA_USER_ID),
        FIRST_NAME: user_data.get("firstName") or headers.get(HEADER_MLISA_USER_FIRST_NAME),
        LAST_NAME: user_data.get("lastName") or headers.get(HEADER_MLISA_USER_LAST_NAME),
        EMAIL: user_data.get("email") or headers.get(HEADER_MLISA_USER_EMAIL),
        MLISA_ROLE: user_data.get("role") or headers.get(HEADER_MLISA_USER_DATA_STUDIO_ROLE),
        ALTO_ROLE: user_data.get("role") or headers.get(HEADER_MLISA_USER_LOWEST_ROLE),
        OWN_TENANT_ID: user_data.get("tenantId") or headers.get(HEADER_MLISA_OWN_TENANT_ID),
        PARENT_TENANT_ID: user_data.get("parentTenantId") or headers.get(HEADER_MLISA_PARENT_TENANT_ID),
        TENANT_IDS: user_data.get("tenantIds") or headers.get(HEADER_MLISA_TENANT_IDS),
        IS_FRANCHISOR: user_data.get("isFranchisor") or headers.get(HEADER_MLISA_IS_SUPER_TENANT, "false"),
    }

    return user_info


def extract_names_from_email(email: str) -> Optional[Dict[str, str]]:
    """Extract first name and last name from email address.

    Args:
        email: Email address

    Returns:
        Dictionary with first_name and last_name, or None if extraction fails
    """
    if not email or "@" not in email:
        return None

    try:
        # Get the local part of the email (before @)
        local_part = email.split("@")[0]

        # Remove common separators and numbers
        clean_name = re.sub(r'[._-]', ' ', local_part)
        clean_name = re.sub(r'\d+', '', clean_name)
        clean_name = clean_name.strip()

        # Split into parts
        name_parts = clean_name.split()

        if len(name_parts) >= 2:
            return {
                "first_name": name_parts[0].capitalize(),
                "last_name": name_parts[1].capitalize()
            }
        elif len(name_parts) == 1:
            return {
                "first_name": name_parts[0].capitalize(),
                "last_name": ""
            }

        return None

    except Exception as e:
        logger.error("Error extracting names from email %s: %s", email, e)
        return None


def _validate_user_info_format(user_info: Dict[str, Any]) -> bool:
    """Validate the format of user information fields.

    Args:
        user_info: User information dictionary

    Returns:
        True if all fields are valid, False otherwise
    """
    # Validate email format
    email = user_info.get(EMAIL)
    if email and not _is_valid_email(email):
        logger.error("Invalid email format: %s", email)
        return False

    # Validate username length
    username = user_info.get(USER_ID)
    if username and len(username) > MAX_USERNAME_LENGTH:
        logger.error("Username too long: %s", username)
        return False

    # Validate email length
    if email and len(email) > MAX_EMAIL_LENGTH:
        logger.error("Email too long: %s", email)
        return False

    # Validate names contain only allowed characters
    first_name = user_info.get(FIRST_NAME)
    last_name = user_info.get(LAST_NAME)

    if first_name and not _is_valid_name(first_name):
        logger.error("Invalid first name format: %s", first_name)
        return False

    if last_name and not _is_valid_name(last_name):
        logger.error("Invalid last name format: %s", last_name)
        return False

    return True


def _is_valid_email(email: str) -> bool:
    """Check if email format is valid.

    Args:
        email: Email address to validate

    Returns:
        True if email format is valid, False otherwise
    """
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(email_pattern, email))


def _is_valid_name(name: str) -> bool:
    """Check if name contains only allowed characters.

    Args:
        name: Name to validate

    Returns:
        True if name is valid, False otherwise
    """
    # Allow letters, spaces, hyphens, and apostrophes
    name_pattern = r"^[a-zA-Z\s\-']+$"
    return bool(re.match(name_pattern, name))


def is_match(pattern: str, text: str) -> bool:
    """Check if text matches the given pattern.

    Args:
        pattern: Regular expression pattern
        text: Text to match against

    Returns:
        True if text matches pattern, False otherwise
    """
    try:
        return bool(re.match(pattern, text))
    except re.error as e:
        logger.error("Invalid regex pattern %s: %s", pattern, e)
        return False


def sanitize_user_input(input_string: str) -> str:
    """Sanitize user input to prevent injection attacks.

    Args:
        input_string: Input string to sanitize

    Returns:
        Sanitized string
    """
    if not input_string:
        return ""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\';()&+]', '', input_string)

    # Limit length
    sanitized = sanitized[:200]

    return sanitized.strip()


def get_user_display_name(user_info: Dict[str, Any]) -> str:
    """Get a display name for the user.

    Args:
        user_info: User information dictionary

    Returns:
        Display name string
    """
    first_name = user_info.get(FIRST_NAME, "")
    last_name = user_info.get(LAST_NAME, "")
    email = user_info.get(EMAIL, "")
    user_id = user_info.get(USER_ID, "")

    if first_name and last_name:
        return f"{first_name} {last_name}"
    elif first_name:
        return first_name
    elif email:
        return email
    else:
        return user_id


def validate_role_assignment(role: str, user_info: Dict[str, Any]) -> bool:
    """Validate if a role can be assigned to a user.

    Args:
        role: Role name to validate
        user_info: User information dictionary

    Returns:
        True if role assignment is valid, False otherwise
    """
    from mlisa.backend.utils.constants import ADMIN, NETWORK_ADMIN, REPORT_ONLY, GUEST

    valid_roles = [ADMIN, NETWORK_ADMIN, REPORT_ONLY, GUEST]

    if role not in valid_roles:
        logger.error("Invalid role: %s", role)
        return False

    # Additional role-specific validation can be added here
    # For example, checking if user has permission to be assigned admin role

    return True