import logging
import json

from flask import request, g
from superset.extensions import cache_manager
from superset.utils.hashing import md5_sha_from_str

from mlisa.backend.utils.constants import (
    HEADER_MLISA_PARENT_TENANT_ID, HEADER_MLISA_TENANT_IDS, HEADER_MLISA_OWN_TENANT_ID,
    HEADER_MLISA_IS_SUPER_TENANT, HEADER_X_USER_CACHE_KEY,
    OWN_TENANT_ID, PARENT_TENANT_ID, TENANT_IDS, IS_FRANCHISOR, USER_ID
)

logger = logging.getLogger(__name__)

class RlsCacheManager:

    def __init__(self, user_id=None):
        self.user_id = user_id or g.user.username
        self.own_tenant_id = request.headers.get(HEADER_MLISA_OWN_TENANT_ID)
        self.tenant_ids = json.loads(request.headers.get(HEADER_MLISA_TENANT_IDS) or "[]")
        self.is_franchisor = request.headers.get(HEADER_MLISA_IS_SUPER_TENANT)
        self.parent_tenant_id = request.headers.get(HEADER_MLISA_PARENT_TENANT_ID)

    def set_user_info_in_cache(self):
        logger.debug("Setting user info in cache for user: %s", self.user_id)
        user_info = {
            USER_ID: self.user_id,
            OWN_TENANT_ID: self.own_tenant_id,
            IS_FRANCHISOR: self.is_franchisor,
            TENANT_IDS: self.tenant_ids,
            PARENT_TENANT_ID: self.parent_tenant_id
        }
        user_info_cache_key = md5_sha_from_str(json.dumps(user_info))
        cache_manager.rls_cache.set(user_info_cache_key, user_info)
        return user_info_cache_key, user_info

    def get_user_info_from_cache(self, user_info_cache_key=None):
        try:
            user_info = cache_manager.rls_cache.get(user_info_cache_key)
            if user_info:
                logger.debug("Getting user info from cache: %s", user_info)
                return user_info

            return self.set_user_info_in_cache()[1]
        except Exception as e:
            logger.error("Error while getting user info in cache for user %s. Failed with error %s", self.user_id, e)
            return {}

    def get_user_info(self, user_id):
        """Get user info for a specific user (compatibility method)."""
        try:
            # Try to get from current cache
            user_info_cache_key = request.headers.get(HEADER_X_USER_CACHE_KEY)
            if user_info_cache_key:
                user_info = self.get_user_info_from_cache(user_info_cache_key)
                if user_info.get(USER_ID) == user_id:
                    return user_info

            # If not found, return empty dict
            return {}
        except Exception as e:
            logger.error("Error getting user info for user %s: %s", user_id, e)
            return {}

    def get_rls_cache_key(self):
        # If "X-User-Cache-Key" is present in the request header, use that
        # as these headers now won't be sent in all requests
        user_info_cache_key = request.headers.get(HEADER_X_USER_CACHE_KEY)
        if user_info_cache_key:
            logger.debug("get_rls_cache_key >> user_info_cache_key: %s", user_info_cache_key)
            user_info = self.get_user_info_from_cache(user_info_cache_key)
            self.own_tenant_id = user_info.get(OWN_TENANT_ID)
            self.tenant_ids = user_info.get(TENANT_IDS)
            self.is_franchisor = user_info.get(IS_FRANCHISOR)
            self.parent_tenant_id = user_info.get(PARENT_TENANT_ID)

        if not self.is_all_values_valid():
            logger.warning("Required info missing. Setting RLS cache key to: NoData4U")
            return "NoData4U"

        rls_cache_key = md5_sha_from_str(f"{self.user_id}:{self.own_tenant_id}:{','.join(self.tenant_ids)}:{self.parent_tenant_id}")
        logger.debug("RLS cache key: %s", rls_cache_key)
        return rls_cache_key

    def is_all_values_valid(self):
        return all([self.own_tenant_id, self.tenant_ids, self.is_franchisor])

    def get_rls_from_cache(self):
        try:
            from superset.views.base import is_user_admin
            if is_user_admin():
                logger.info("Super admin user requesting RLS")
                return {"switch": "1 = 1", "ap": "1 = 1"}

            rls_cache_key = self.get_rls_cache_key()
            rls_clause = cache_manager.rls_cache.get(rls_cache_key)
            if rls_clause:
                logger.info("RLS clause found in REDIS cache")
                return rls_clause

            return self.set_rls_in_cache()
        except Exception as e:
            logger.error("Error while getting RLS in cache for user %s. Failed with error %s", self.user_id, e)
            return None

    def set_rls_in_cache(self):
        try:
            rls_cache_key = self.get_rls_cache_key()
            if rls_cache_key == "NoData4U":
                rls_clause = {"switch": "1 = 0", "ap": "1 = 0"}
            else:
                # Import here to avoid circular import
                from mlisa.backend.security.rbac_helper import RBACHelper
                rls_clause = RBACHelper().get_rls_filter_clause(
                    user_id=self.user_id,
                    tenant_ids=json.dumps(self.tenant_ids),
                    is_franchisor=self.is_franchisor,
                    own_tenant_id=self.own_tenant_id,
                    parent_tenant_id=self.parent_tenant_id,
                )
            cache_manager.rls_cache.set(rls_cache_key, rls_clause)
            logger.debug("Setting RLS in cache: %s", rls_clause)
            return rls_clause
        except Exception as e:
            logger.error("Error while setting RLS in cache for user %s. Failed with error %s", self.user_id, e)
            return None

    def delete_rls_from_cache(self):
        """Delete RLS data from cache."""
        try:
            rls_cache_key = self.get_rls_cache_key()
            cache_manager.rls_cache.delete(rls_cache_key)
            logger.info("Deleted RLS from cache for user: %s", self.user_id)
            return True
        except Exception as e:
            logger.error("Error deleting RLS from cache for user %s: %s", self.user_id, e)
            return False


class SessionCacheManager:
    """Session cache manager for compatibility."""

    def __init__(self, user_id):
        self.user_id = user_id

    def delete_session_data(self):
        """Delete session data."""
        logger.debug("Deleting session data for user: %s", self.user_id)
        return True