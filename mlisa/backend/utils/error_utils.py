import logging
from typing import Optional

from flask import jsonify, Response

logger = logging.getLogger(__name__)


def json_error_response(
    err_msg: str,
    code: str = "CODE-10000",
    status: int = 500,
    reason: Optional[str] = None,
    suggestion: Optional[str] = None
) -> Response:
    """Create a standardized JSON error response.

    Args:
        err_msg: Error message
        code: Error code
        status: HTTP status code
        reason: Optional reason for the error
        suggestion: Optional suggestion for resolving the error

    Returns:
        Flask Response object with JSON error
    """
    error_response = {
        "error": {
            "message": err_msg,
            "code": code,
            "status": status,
        }
    }

    if reason:
        error_response["error"]["reason"] = reason

    if suggestion:
        error_response["error"]["suggestion"] = suggestion

    logger.error("Error response [%s]: %s", code, err_msg)

    return jsonify(error_response), status


def validation_error_response(field: str, message: str) -> Response:
    """Create a validation error response.

    Args:
        field: Field that failed validation
        message: Validation error message

    Returns:
        Flask Response object with validation error
    """
    return json_error_response(
        err_msg=f"Validation error for field '{field}': {message}",
        code="VALIDATION-ERROR",
        status=400,
        reason="Invalid input data",
        suggestion="Please check the input data and try again"
    )


def authentication_error_response(message: str = "Authentication failed") -> Response:
    """Create an authentication error response.

    Args:
        message: Authentication error message

    Returns:
        Flask Response object with authentication error
    """
    return json_error_response(
        err_msg=message,
        code="AUTH-ERROR",
        status=401,
        reason="Authentication credentials are invalid or missing",
        suggestion="Please provide valid authentication credentials"
    )


def authorization_error_response(message: str = "Access denied") -> Response:
    """Create an authorization error response.

    Args:
        message: Authorization error message

    Returns:
        Flask Response object with authorization error
    """
    return json_error_response(
        err_msg=message,
        code="AUTHZ-ERROR",
        status=403,
        reason="User does not have permission to access this resource",
        suggestion="Please contact your administrator for access"
    )


def not_found_error_response(resource: str = "Resource") -> Response:
    """Create a not found error response.

    Args:
        resource: Name of the resource that was not found

    Returns:
        Flask Response object with not found error
    """
    return json_error_response(
        err_msg=f"{resource} not found",
        code="NOT-FOUND",
        status=404,
        reason="The requested resource does not exist",
        suggestion="Please check the resource identifier and try again"
    )


def internal_server_error_response(message: str = "Internal server error") -> Response:
    """Create an internal server error response.

    Args:
        message: Error message

    Returns:
        Flask Response object with internal server error
    """
    return json_error_response(
        err_msg=message,
        code="INTERNAL-ERROR",
        status=500,
        reason="An unexpected error occurred on the server",
        suggestion="Please try again later or contact support"
    )


def bad_request_error_response(message: str = "Bad request") -> Response:
    """Create a bad request error response.

    Args:
        message: Error message

    Returns:
        Flask Response object with bad request error
    """
    return json_error_response(
        err_msg=message,
        code="BAD-REQUEST",
        status=400,
        reason="The request is malformed or contains invalid data",
        suggestion="Please check the request format and data"
    )