# MLISA Data Studio Constants
# This file contains all constants used across the MLISA Data Studio backend

# Role Constants
ADMIN = "admin"
NETWORK_ADMIN = "network-admin"
REPORT_ONLY = "report-only"
RUCKUS_ADMIN = "Admin"
PUBLIC = "Public"
GUEST = "Guest"

# Security Constants
USER_SECRET = "CHANGE_ME_IN_PRODUCTION"  # MUST be overridden in production via environment variable

# Dataset Name Mapping
MLISA_DATASET_NAME_MAPPING = {
    "apAlarms": "AP Alarms",
    "apInventory": "AP Inventory",
    "apEvents": "AP Events",
    "apWiredClient": "AP Wired Device",
    "binnedApTraffic": "AP Info and Statistics",
    "binnedAvc": "Applications",
    "binnedRadio": "AP Airtime and Hardware",
    "binnedSessions": "Client Info and Statistics",
    "controllerInventory": "Controller Inventory",
    "mlisa-apAfc": "AP AFC",
    "mlisa-apConnectionStats": "Client Connection Counts",
    "mlisa-apMesh": "AP Mesh",
    "mlisa-apPeer": "AP Peer",
    "mlisa-clientConnectionStats": "Client Connection Events",
    "mlisa-edgeAvc": "Edge AVC",
    "rogueAp": "AP Rogues",
    "sessionsSummary": "Client Sessions",
    "switchInventory": "Switch Inventory",
    "switchNetwork": "Switch Network",
    "wifiCalling": "AP WiFi Calling",
}

# Feature Flag Constants
RA_PRIVACY_SETTINGS_APP_VISIBILITY_TOGGLE = "ra-privacysettings-app-visibility-toggle"
RUCKUS_AI_REPORT_PDF_COVER_PAGE_TOGGLE = "ruckus-ai-report-pdf-cover-page-toggle"
ACX_UI_REPORT_PDF_COVER_PAGE_TOGGLE = "acx-ui-report-pdf-cover-page-toggle"

# HTTP Headers from MLISA RBAC
HEADER_MLISA_USER_ID = "x-mlisa-user-id"
HEADER_MLISA_USER_LOWEST_ROLE = "x-mlisa-user-lowest-role"
HEADER_MLISA_OWN_TENANT_ID = "x-mlisa-own-tenant-id"
HEADER_MLISA_TENANT_IDS = "x-mlisa-tenant-ids"
HEADER_MLISA_IS_SUPER_TENANT = "x-mlisa-is-super-tenant"
HEADER_MLISA_USER_FIRST_NAME = "x-mlisa-user-firstname"
HEADER_MLISA_USER_LAST_NAME = "x-mlisa-user-lastname"
HEADER_MLISA_USER_EMAIL = "x-mlisa-user-email"
HEADER_MLISA_USER_DATA_STUDIO_ROLE = "x-mlisa-user-datastudio-role"
HEADER_MLISA_IS_SUPPORT = "x-mlisa-is-support"
HEADER_MLISA_PARENT_TENANT_ID = "x-mlisa-parent-tenant-id"

# User Info Constants
USER_ID = "user_id"
EMAIL = "email"
FIRST_NAME = "first_name"
LAST_NAME = "last_name"
MLISA_ROLE = "mlisa_role"
ALTO_ROLE = "alto_role"
IS_FRANCHISOR = "is_franchisor"
OWN_TENANT_ID = "own_tenant_id"
PARENT_TENANT_ID = "parent_tenant_id"
TENANT_IDS = "tenant_ids"

# Role Prefixes
OWN_TENANT_ID_PREFIX = "own_tenant_"
TENANT_ID_PREFIX = "tenant_"

# Schedule Constants
DAILY = "DAILY"
WEEKLY = "WEEKLY"
MONTHLY = "MONTHLY"

# Date/Time Format Constants
FILE_DATE_TIME_FORMAT = "%Y%m%d_%H%M%S"
PDF_CLIENT_DATE_TIME_FORMAT = "%b %d, %Y %I:%M %p"
SQL_DATE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S"

# Application Constants
APP_VISIBILITY = "appVisibility"
ACX_ACCOUNT_TIER = "acx_account_tier"
R1_LICENSE_SILVER = "silver"
EMBEDDED_DASHBOARD_PREFIX = "embedded"

# Filter and RLS Constants
ACX_FILTER = "acx_filter"
API_SCHEDULE = "api_schedule"
API_SCHEDULE_RETRY = "api_schedule_retry"
EMBEDDED_DASHBOARD_SCHEDULE = "embedded_dashboard_schedule"
FILTERS = "filters"
NATIVE_FILTER = "native_filter"
NATIVE_FILTER_TIME_GRAIN = "native_filter_time_grain"
QUERY_END_TIME = "query_end_time"
QUERY_START_TIME = "query_start_time"
RLS_CLAUSE = "rls_clause"
SCHEDULE_GRANULARITY = "schedule_granularity"

# Error Codes
ERROR_CODE_AUTHENTICATION_FAILED = "DATASTUDIO-10001"
ERROR_CODE_MISSING_HEADERS = "DATASTUDIO-10002"
ERROR_CODE_ROLE_CREATION_FAILED = "DATASTUDIO-10003"
ERROR_CODE_USER_AUTH_FAILED = "DATASTUDIO-10004"

# API Constants
DEFAULT_PAGE_SIZE = 25
MAX_PAGE_SIZE = 100

# Cache Constants
CACHE_TIMEOUT_SHORT = 300  # 5 minutes
CACHE_TIMEOUT_MEDIUM = 1800  # 30 minutes
CACHE_TIMEOUT_LONG = 3600  # 1 hour

# Validation Constants
MIN_PASSWORD_LENGTH = 8
MAX_USERNAME_LENGTH = 50
MAX_EMAIL_LENGTH = 100

# RLS Constants
START_DOMAIN_LEVEL = 1

# Key Label Mapping for RLS Filters
KEY_LABEL_DICT = {
    "domain": "domain",
    "site": "site",
    "ap": "apMac",
    "apMac": "apMac",
    "switch": "switchMac",
    "switchMac": "switchMac",
    "zone": "zone",
    "venue": "venue",
    "controller": "controller"
}

# RLS Filter Constants
RLS_RULES = "rls_rules"
CLAUSE = "clause"
EXCLUDED = "excluded"
FILTER = "filter"
NATIVE_FILTER_CONFIG = "native_filter_config"
EXTRA_FORM_DATA = "extra_form_data"
SCOPE = "scope"
NATIVE_FILTER_TIME_RANGE = "time_range"
NATIVE_FILTER_TIME_GRAIN = "time_grain"
TIME_GRAIN_SQLA = "time_grain_sqla"

# Cache Constants
HEADER_X_USER_CACHE_KEY = "x-user-cache-key"
CACHE_KEY = "cache_key"
DEPLOYMENT = "deployment"