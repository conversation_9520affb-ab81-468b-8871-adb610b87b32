import os
import logging
import requests
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)

# Configuration
MLISA_RBAC_BASE_URL = os.environ.get("MLISA_RBAC_URL", "http://localhost:3000")


def make_api_call(url: str, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
    """Make HTTP API call to MLISA RBAC service.

    Args:
        url: The API endpoint URL
        headers: Optional HTTP headers

    Returns:
        JSON response data or None if failed
    """
    logger.debug("Request[MLISA-RBAC] :: [%s]", url)
    logger.debug("Req Headers :: [%s]", headers)

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.ok:
            logger.debug("Response[MLISA-RBAC] :: [%s]", response.text)
            return response.json()
        else:
            logger.error("Error :: Status %d, Response: %s", response.status_code, response.text)
            return None
    except requests.ConnectionError as e:
        logger.error("ConnectionError [MLISA-RBAC] :: %s", e)
        return None
    except requests.Timeout as e:
        logger.error("Timeout [MLISA-RBAC] :: %s", e)
        return None
    except requests.RequestException as e:
        logger.error("RequestException [MLISA-RBAC] :: %s", e)
        return None
    except Exception as e:
        logger.error("Unexpected error [MLISA-RBAC] :: %s", e)
        return None


def get_user_resource_filter(tenant_ids: str, user_id: str, is_franchisor: str) -> Optional[Dict[str, Any]]:
    """Get resource filter for a specific user.

    Args:
        tenant_ids: JSON string of tenant IDs
        user_id: User identifier
        is_franchisor: Whether user is a franchisor ("true"/"false")

    Returns:
        Resource filter data or None if failed
    """
    headers = {
        "x-mlisa-tenant-ids": tenant_ids,
        "x-mlisa-user-id": user_id,
        "x-mlisa-is-franchisor": is_franchisor,
    }
    url = f"{MLISA_RBAC_BASE_URL}/resourceFilter"

    logger.info("Getting resource filter for user: %s, franchisor: %s", user_id, is_franchisor)
    result = make_api_call(url, headers)

    if result:
        logger.info("Successfully retrieved resource filter for user: %s", user_id)
    else:
        logger.warning("Failed to retrieve resource filter for user: %s", user_id)

    return result


def get_authentications(tenant_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """Get authentication settings for a tenant.

    Args:
        tenant_id: Tenant identifier

    Returns:
        Authentication settings or None if failed
    """
    headers = {
        "x-mlisa-tenant-id": str(tenant_id) if tenant_id else ""
    }
    url = f"{MLISA_RBAC_BASE_URL}/tenantSettings/authentications"

    logger.info("Getting authentication settings for tenant: %s", tenant_id)
    result = make_api_call(url, headers)

    if result:
        logger.info("Successfully retrieved authentication settings for tenant: %s", tenant_id)
    else:
        logger.warning("Failed to retrieve authentication settings for tenant: %s", tenant_id)

    return result


def get_account_info(tenant_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """Get account information for a tenant.

    Args:
        tenant_id: Tenant identifier

    Returns:
        Account information or None if failed
    """
    headers = {
        "x-mlisa-tenant-id": str(tenant_id) if tenant_id else ""
    }
    url = f"{MLISA_RBAC_BASE_URL}/account/info"

    logger.info("Getting account info for tenant: %s", tenant_id)
    result = make_api_call(url, headers)

    if result:
        logger.info("Successfully retrieved account info for tenant: %s", tenant_id)
    else:
        logger.warning("Failed to retrieve account info for tenant: %s", tenant_id)

    return result


def get_tenant_settings(tenant_id: str, setting_type: str) -> Optional[Dict[str, Any]]:
    """Get specific tenant settings.

    Args:
        tenant_id: Tenant identifier
        setting_type: Type of setting to retrieve

    Returns:
        Tenant settings or None if failed
    """
    headers = {
        "x-mlisa-tenant-id": tenant_id
    }
    url = f"{MLISA_RBAC_BASE_URL}/tenantSettings/{setting_type}"

    logger.info("Getting %s settings for tenant: %s", setting_type, tenant_id)
    result = make_api_call(url, headers)

    if result:
        logger.info("Successfully retrieved %s settings for tenant: %s", setting_type, tenant_id)
    else:
        logger.warning("Failed to retrieve %s settings for tenant: %s", setting_type, tenant_id)

    return result


def validate_user_access(user_id: str, tenant_ids: List[str], resource_type: str) -> bool:
    """Validate if user has access to specific resources.

    Args:
        user_id: User identifier
        tenant_ids: List of tenant IDs
        resource_type: Type of resource to validate access for

    Returns:
        True if user has access, False otherwise
    """
    headers = {
        "x-mlisa-user-id": user_id,
        "x-mlisa-tenant-ids": ",".join(tenant_ids),
        "x-mlisa-resource-type": resource_type,
    }
    url = f"{MLISA_RBAC_BASE_URL}/access/validate"

    logger.info("Validating access for user: %s, resource: %s", user_id, resource_type)
    result = make_api_call(url, headers)

    if result and isinstance(result, dict):
        access_granted = result.get("access", False)
        logger.info("Access validation for user %s: %s", user_id, access_granted)
        return access_granted

    logger.warning("Access validation failed for user: %s", user_id)
    return False


def get_user_roles(user_id: str, tenant_id: Optional[str] = None) -> List[str]:
    """Get roles for a specific user.

    Args:
        user_id: User identifier
        tenant_id: Optional tenant identifier

    Returns:
        List of role names
    """
    headers = {
        "x-mlisa-user-id": user_id,
    }
    if tenant_id:
        headers["x-mlisa-tenant-id"] = tenant_id

    url = f"{MLISA_RBAC_BASE_URL}/user/roles"

    logger.info("Getting roles for user: %s", user_id)
    result = make_api_call(url, headers)

    if result and isinstance(result, dict):
        roles = result.get("roles", [])
        logger.info("Retrieved %d roles for user: %s", len(roles), user_id)
        return roles

    logger.warning("Failed to retrieve roles for user: %s", user_id)
    return []


def create_user_session(user_id: str, tenant_id: str, session_data: Dict[str, Any]) -> bool:
    """Create user session in RBAC service.

    Args:
        user_id: User identifier
        tenant_id: Tenant identifier
        session_data: Session data to store

    Returns:
        True if session created successfully, False otherwise
    """
    headers = {
        "x-mlisa-user-id": user_id,
        "x-mlisa-tenant-id": tenant_id,
        "Content-Type": "application/json",
    }
    url = f"{MLISA_RBAC_BASE_URL}/session/create"

    try:
        response = requests.post(url, headers=headers, json=session_data, timeout=30)
        if response.ok:
            logger.info("Successfully created session for user: %s", user_id)
            return True
        else:
            logger.error("Failed to create session for user %s: %s", user_id, response.text)
            return False
    except Exception as e:
        logger.error("Error creating session for user %s: %s", user_id, e)
        return False


def invalidate_user_session(user_id: str) -> bool:
    """Invalidate user session in RBAC service.

    Args:
        user_id: User identifier

    Returns:
        True if session invalidated successfully, False otherwise
    """
    headers = {
        "x-mlisa-user-id": user_id,
    }
    url = f"{MLISA_RBAC_BASE_URL}/session/invalidate"

    try:
        response = requests.delete(url, headers=headers, timeout=30)
        if response.ok:
            logger.info("Successfully invalidated session for user: %s", user_id)
            return True
        else:
            logger.error("Failed to invalidate session for user %s: %s", user_id, response.text)
            return False
    except Exception as e:
        logger.error("Error invalidating session for user %s: %s", user_id, e)
        return False


class RBACAdapter:
    """Adapter class for integrating with external RBAC services."""

    def __init__(self, base_url: Optional[str] = None):
        """Initialize RBAC adapter.

        Args:
            base_url: Optional base URL for RBAC service
        """
        self.base_url = base_url or MLISA_RBAC_BASE_URL

    def get_user_permissions(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Get comprehensive user permissions.

        Args:
            user_id: User identifier
            tenant_id: Tenant identifier

        Returns:
            Dictionary containing user permissions and access rights
        """
        resource_filter = get_user_resource_filter(
            tenant_ids=f'["{tenant_id}"]',
            user_id=user_id,
            is_franchisor="false"
        )

        roles = get_user_roles(user_id, tenant_id)

        return {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "roles": roles,
            "resource_filter": resource_filter,
            "timestamp": os.time(),
        }

    def validate_api_access(self, user_id: str, endpoint: str, method: str) -> bool:
        """Validate if user can access specific API endpoint.

        Args:
            user_id: User identifier
            endpoint: API endpoint
            method: HTTP method

        Returns:
            True if access is allowed, False otherwise
        """
        headers = {
            "x-mlisa-user-id": user_id,
            "x-mlisa-endpoint": endpoint,
            "x-mlisa-method": method,
        }
        url = f"{self.base_url}/api/access/validate"

        result = make_api_call(url, headers)

        if result and isinstance(result, dict):
            return result.get("allowed", False)

        return False