import logging

from flask import Response, request
from flask_appbuilder import expose
from flask_appbuilder.api import <PERSON><PERSON><PERSON>
from flask_appbuilder.security.decorators import permission_name
from superset.constants import MODEL_API_RW_METHOD_PERMISSION_MAP

from mlisa.backend.security.rbac_helper import RBACHelper

logger = logging.getLogger(__name__)


class MLISASecurityApi(BaseApi):
    """API endpoints for MLISA security and tenant data operations."""

    class_permission_name = "Database"
    method_permission_name = MODEL_API_RW_METHOD_PERMISSION_MAP
    resource_name = "security"
    allow_browser_login = True
    openapi_spec_tag = "Security"
    rbac_helper = RBACHelper()

    @expose("/tenant/users", methods=["GET"])
    @permission_name("get")
    def get_tenant_users(self) -> Response:
        """Get list of users for the current tenant based on resource type.

        Query Parameters:
            resource: Type of resource ('dashboard', 'chart', 'report')

        Returns:
            JSON response with tenant users list
        """
        from superset.extensions import db
        from flask_appbuilder.security.sqla.models import User

        tenant_user_ids = []
        resource = request.args.get("resource")

        if not resource:
            return self.response_400(message="Resource parameter is required")

        try:
            if resource == "dashboard":
                from superset.models.dashboard import dashboard_user

                dashboard_ids = self.rbac_helper.get_tenant_dashboards()
                if dashboard_ids:
                    tenant_user_ids = [
                        id[0]
                        for id in db.session.query(dashboard_user.c.user_id)
                        .filter(dashboard_user.c.dashboard_id.in_(dashboard_ids))
                        .all()
                    ]
            elif resource == "chart":
                from superset.models.slice import slice_user

                chart_ids = self.rbac_helper.get_tenant_charts()
                if chart_ids:
                    tenant_user_ids = [
                        id[0]
                        for id in db.session.query(slice_user.c.user_id)
                        .filter(slice_user.c.slice_id.in_(chart_ids))
                        .all()
                    ]
            elif resource == "report":
                from superset.reports.models import report_schedule_user

                report_ids = self.rbac_helper.get_tenant_scheduled_reports()
                if report_ids:
                    tenant_user_ids = [
                        id[0]
                        for id in db.session.query(report_schedule_user.c.user_id)
                        .filter(report_schedule_user.c.report_schedule_id.in_(report_ids))
                        .all()
                    ]
            else:
                return self.response_400(message=f"Invalid resource type: {resource}")

            # Get unique user details
            unique_user_ids = list(set(tenant_user_ids))
            if unique_user_ids:
                users = db.session.query(User).filter(User.id.in_(unique_user_ids)).all()
                tenant_users = sorted(
                    [
                        {
                            "label": f"{user.first_name} {user.last_name}".strip(),
                            "value": user.id,
                            "email": user.email,
                            "username": user.username,
                        }
                        for user in users
                        if user.first_name or user.last_name
                    ],
                    key=lambda d: d["label"].lower(),
                )
            else:
                tenant_users = []

            logger.debug(
                "[/tenant/users] Resource is %s and found %d tenant users",
                resource,
                len(tenant_users),
            )

            response = {
                "tenant_users": tenant_users,
                "count": len(tenant_users),
                "resource": resource,
            }
            return self.response(200, result=response)

        except Exception as e:
            logger.error("Error fetching tenant users for resource %s: %s", resource, e)
            return self.response_500(message="Failed to fetch tenant users")

    @expose("/tenant/owner/resource", methods=["GET"])
    @permission_name("get")
    def get_tenant_resource(self) -> Response:
        """Get list of resources owned by the current user in the tenant.

        Query Parameters:
            resource: Type of resource ('dashboard', 'chart')

        Returns:
            JSON response with tenant resources owned by current user
        """
        from superset.extensions import db
        from superset.models.dashboard import Dashboard
        from superset import security_manager
        from flask_login import current_user

        resource = request.args.get("resource")

        if not resource:
            return self.response_400(message="Resource parameter is required")

        if not current_user.is_authenticated:
            return self.response_401(message="Authentication required")

        try:
            tenant_resource = []
            current_user_id = security_manager.user_model.get_user_id()

            if resource == "dashboard":
                tenant_dashboard_ids = self.rbac_helper.get_tenant_dashboards()
                if tenant_dashboard_ids:
                    # Get dashboards owned by current user
                    owned_dashboard_ids = (
                        db.session.query(Dashboard.id)
                        .join(Dashboard.owners)
                        .filter(security_manager.user_model.id == current_user_id)
                        .filter(Dashboard.id.in_(tenant_dashboard_ids))
                        .all()
                    )

                    if owned_dashboard_ids:
                        owned_ids = [dashboard_id[0] for dashboard_id in owned_dashboard_ids]
                        dashboards = db.session.query(Dashboard).filter(
                            Dashboard.id.in_(owned_ids)
                        ).all()

                        tenant_resource = sorted(
                            [
                                {
                                    "label": dashboard.dashboard_title,
                                    "dashboardId": dashboard.id,
                                    "url": dashboard.url,
                                    "published": dashboard.published,
                                }
                                for dashboard in dashboards
                            ],
                            key=lambda d: d["label"].lower(),
                        )

            elif resource == "chart":
                from superset.models.slice import Slice

                tenant_chart_ids = self.rbac_helper.get_tenant_charts()
                if tenant_chart_ids:
                    # Get charts owned by current user
                    owned_chart_ids = (
                        db.session.query(Slice.id)
                        .join(Slice.owners)
                        .filter(security_manager.user_model.id == current_user_id)
                        .filter(Slice.id.in_(tenant_chart_ids))
                        .all()
                    )

                    if owned_chart_ids:
                        owned_ids = [chart_id[0] for chart_id in owned_chart_ids]
                        charts = db.session.query(Slice).filter(
                            Slice.id.in_(owned_ids)
                        ).all()

                        tenant_resource = sorted(
                            [
                                {
                                    "label": chart.slice_name,
                                    "sliceId": chart.id,
                                    "url": chart.url,
                                    "description": chart.description,
                                }
                                for chart in charts
                            ],
                            key=lambda d: d["label"].lower(),
                        )
            else:
                return self.response_400(message=f"Invalid resource type: {resource}")

            logger.debug(
                "[/tenant/owner/resource] Resource is %s and found %d owned resources",
                resource,
                len(tenant_resource),
            )

            response = {
                "resources": tenant_resource,
                "count": len(tenant_resource),
                "resource": resource,
                "user_id": current_user_id,
            }
            return self.response(200, result=response)

        except Exception as e:
            logger.error("Error fetching tenant resources for resource %s: %s", resource, e)
            return self.response_500(message="Failed to fetch tenant resources")

    @expose("/tenant/info", methods=["GET"])
    @permission_name("get")
    def get_tenant_info(self) -> Response:
        """Get current tenant information for the authenticated user.

        Returns:
            JSON response with tenant information
        """
        from flask_login import current_user

        if not current_user.is_authenticated:
            return self.response_401(message="Authentication required")

        try:
            tenant_id = self.rbac_helper.get_current_user_tenant_id()
            dashboard_count = len(self.rbac_helper.get_tenant_dashboards())
            chart_count = len(self.rbac_helper.get_tenant_charts())
            report_count = len(self.rbac_helper.get_tenant_scheduled_reports())

            user_info = {
                "user_id": current_user.username,
                "first_name": current_user.first_name,
                "last_name": current_user.last_name,
                "email": current_user.email,
                "roles": [role.name for role in current_user.roles],
            }

            tenant_info = {
                "tenant_id": tenant_id,
                "user": user_info,
                "resource_counts": {
                    "dashboards": dashboard_count,
                    "charts": chart_count,
                    "reports": report_count,
                },
            }

            logger.debug("[/tenant/info] Tenant info: %s", tenant_info)
            return self.response(200, result=tenant_info)

        except Exception as e:
            logger.error("Error fetching tenant info: %s", e)
            return self.response_500(message="Failed to fetch tenant information")

    @expose("/user/permissions", methods=["GET"])
    @permission_name("get")
    def get_user_permissions(self) -> Response:
        """Get current user's permissions.

        Returns:
            JSON response with user permissions
        """
        from flask_login import current_user

        if not current_user.is_authenticated:
            return self.response_401(message="Authentication required")

        try:
            # Get user's permissions from roles
            permissions = set()
            for role in current_user.roles:
                for perm in role.permissions:
                    permissions.add((perm.permission.name, perm.view_menu.name))

            permissions_list = [
                {"permission": perm[0], "view_menu": perm[1]}
                for perm in sorted(permissions)
            ]

            response = {
                "user_id": current_user.username,
                "roles": [role.name for role in current_user.roles],
                "permissions": permissions_list,
                "permission_count": len(permissions_list),
            }

            logger.debug("[/user/permissions] User %s has %d permissions",
                        current_user.username, len(permissions_list))
            return self.response(200, result=response)

        except Exception as e:
            logger.error("Error fetching user permissions: %s", e)
            return self.response_500(message="Failed to fetch user permissions")