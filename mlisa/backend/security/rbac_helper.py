import logging
from typing import List, Optional

from superset import db, is_feature_enabled
from flask_appbuilder.security.sqla.models import User, Role
from sqlalchemy.exc import SQLAlchemyError

from mlisa.backend.utils.constants import (
    APP_VISIBILITY,
    START_DOMAIN_LEVEL,
    KEY_LABEL_DICT,
)
from mlisa.backend.utils.cache_manager import RlsCacheManager

logger = logging.getLogger(__name__)


class RBACHelper:
    """Helper class for managing RBAC operations in MLISA Data Studio."""

    def create_role(self, role) -> Role:
        """Create a new role or return existing role."""
        from superset import security_manager

        existing_role = security_manager.find_role(role)
        if not existing_role:
            new_role = security_manager.add_role(role)
            return new_role
        return existing_role

    def add_users(self, users_data: List[dict]) -> List[User]:
        """Add users to Superset from a list of user data dictionaries.

        Args:
            users_data: A list of dictionaries containing user data.
                Each dictionary should have the following keys:
                    - username
                    - firstName
                    - lastName
                    - email
                    - uiRole
                    - otherRoles (optional)

        Returns:
            A list of User objects created from the input data.
        """
        from superset import security_manager
        from mlisa.backend.utils.constants import USER_SECRET

        created_users = []
        for user_data in users_data:
            username = user_data["username"]
            first_name = user_data["firstName"]
            last_name = user_data["lastName"]
            email = user_data["email"]
            password = USER_SECRET

            ui_role = security_manager.find_role(user_data["uiRole"])
            if not ui_role:
                logger.error("UI role %s not found", user_data["uiRole"])
                return []

            roles = [ui_role]
            if is_feature_enabled("ROW_LEVEL_SECURITY"):
                roles.extend([self.create_role(role) for role in user_data.get("otherRoles", [])])

            user = security_manager.add_user(username, first_name, last_name, email, roles, password)
            if not user:
                logger.error("Unable to create user %s", username)
                return []
            created_users.append(user)

        return created_users

    def get_tenant_dashboards(self, tenant_id: Optional[str] = None) -> List[int]:
        """Get list of dashboard IDs for the current tenant."""
        from superset.extensions import db
        from superset.models.dashboard import Dashboard
        from sqlalchemy import or_, and_
        from flask_login import current_user
        from superset.models.dashboard import dashboard_user

        if not current_user.is_authenticated:
            logger.warning("Attempting to get tenant dashboards for unauthenticated user")
            return []

        try:
            tenant_ids = self.get_current_user_tenant_id()[0]
            session = db.session()
            # fetching dashboard ids belonging to the tenancy of the current user.
            filter_list = [Dashboard.json_metadata.contains(x) for x in tenant_ids]
            tenant_dashboard_ids = [
                id[0] for id in session.query(Dashboard.id).filter(or_(*filter_list)).all()
            ]
            logger.debug("get_tenant_dashboards -> tenant_dashboard_ids: %s", tenant_dashboard_ids)

            tenant_owner_ids = [
                id[0]
                for id in db.session.query(Dashboard.id)
                .filter(
                    and_(
                        Dashboard.id.in_(
                            db.session.query(dashboard_user.c.dashboard_id)
                            .filter(dashboard_user.c.user_id == current_user.id)
                            .all()
                        ),
                        Dashboard.id.in_(tenant_dashboard_ids),
                    )
                )
                .all()
            ]

            logger.debug(
                "get_tenant_dashboards -> tenant_owner_ids: %s", tenant_owner_ids
            )  # returns current user's dashboard ids in current tenancy

            users_dashboard_ids = [
                id[0]
                for id in db.session.query(Dashboard.id)
                .filter(
                    and_(
                        Dashboard.id.in_(
                            list(set(tenant_dashboard_ids).difference(tenant_owner_ids))
                        ),
                        Dashboard.published == "t",
                    )
                )
                .all()
            ]
            logger.debug(
                "get_tenant_dashboards -> users_dashboard_ids: %s", users_dashboard_ids
            )  # returns published dashboard ids of other users in current tenancy

            dashboard_ids = [
                id[0]
                for id in db.session.query(Dashboard.id)
                .filter(
                    or_(
                        Dashboard.id.in_(tenant_owner_ids),
                        Dashboard.id.in_(users_dashboard_ids),
                    )
                )
                .all()
            ]
            logger.debug("get_tenant_dashboards -> dashboard_ids: %s", dashboard_ids)
            return dashboard_ids

        except SQLAlchemyError as e:
            logger.error("Error fetching tenant dashboards for user %s: %s",
                        current_user.username if current_user.is_authenticated else 'unknown', e)
            return []

    def get_tenant_charts(self, tenant_id: Optional[str] = None) -> List[int]:
        """Get list of chart IDs for the current tenant."""
        from flask_login import current_user
        from superset.models.slice import Slice

        if not current_user.is_authenticated:
            return []

        try:
            # Get charts accessible to current user
            charts = db.session.query(Slice.id).all()

            return [chart.id for chart in charts]
        except SQLAlchemyError as e:
            logger.error("Error fetching tenant charts: %s", e)
            return []

    def get_tenant_scheduled_reports(self, tenant_id: Optional[str] = None) -> List[int]:
        """Get list of scheduled report IDs for the current tenant."""
        from flask_login import current_user
        from superset.reports.models import ReportSchedule

        if not current_user.is_authenticated:
            return []

        try:
            # Get scheduled reports accessible to current user
            reports = db.session.query(ReportSchedule.id).all()

            return [report.id for report in reports]
        except SQLAlchemyError as e:
            logger.error("Error fetching tenant scheduled reports: %s", e)
            return []

    def get_current_user_tenant_id(self) -> Optional[str]:
        """Get the current user's tenant ID."""
        from flask_login import current_user

        if not current_user.is_authenticated:
            return None

        # Extract tenant ID from user roles or properties
        # This implementation depends on how tenant information is stored
        for role in current_user.roles:
            if role.name.startswith("tenant_"):
                return role.name.replace("tenant_", "")

        return None



    def create_rls_filter(self, resource_filter, user_id):
        """Create RLS filter for the user."""
        from superset.connectors.sqla.models import SqlaTable

        session = db.session
        tables = session.query(SqlaTable)
        try:
            role = self.create_role(user_id)
            if resource_filter is None:
                self.add_default_rls_filter(role, session, SqlaTable)
                logger.error(
                    "Create RLS filter: Resource filter not present for user %s. Added the default filter", user_id
                )
            else:
                filter_values = self.__rls_filter(
                    resource_filter, user_id, session, tables, SqlaTable
                )
                self.add_rls_filter(filter_values["switch"])
                self.add_rls_filter(filter_values["ap"])
            session.commit()
        except SQLAlchemyError as ex:
            db.session.rollback()
            logger.error(
                "Create RLS filter: SQLAlchemyError while committing session %s", ex
            )
            return False
        return True

    def update_rls_filter(self, resource_filter, user_id):
        """Update RLS filter for the user."""
        from superset.connectors.sqla.models import SqlaTable

        session = db.session
        tables = session.query(SqlaTable)
        try:
            role = self.create_role(user_id)
            rls_filters = self.get_rls_filter(role)
            tables_list = self.get_list_of_tables(session, SqlaTable)
            if resource_filter is None:
                if len(rls_filters):
                    for rls in rls_filters:
                        if any("switch" in sub.name for sub in rls.tables):
                            rls.clause = '1 = 0'
                            rls.tables = tables_list["switch"]
                        else:
                            rls.clause = '1 = 0'
                            rls.tables = tables_list["ap"]
                else:
                    logger.warning(
                        "Update RLS filter: RLS filter was missing for user %s. Adding the default filter.", user_id
                    )
                    self.add_default_rls_filter(role, session, SqlaTable)
                logger.error(
                    "Update RLS filter: Resource filter not present for user %s", user_id
                )
            else:
                filter_values = self.__rls_filter(
                    resource_filter, user_id, session, tables, SqlaTable
                )
                if len(rls_filters):
                    for rls in rls_filters:
                        if any("switch" in sub.name for sub in rls.tables):
                            rls.clause = filter_values['switch']["clause"]
                            rls.tables = tables_list["switch"]
                        else:
                            rls.clause = filter_values['ap']["clause"]
                            rls.tables = tables_list["ap"]
                else:
                    self.add_rls_filter(filter_values["switch"])
                    self.add_rls_filter(filter_values["ap"])
            session.commit()
        except SQLAlchemyError as ex:
            db.session.rollback()
            logger.error(
                "Update RLS filter: SQLAlchemyError while committing session %s", ex
            )
            return False
        return True

    def cache_user_info(self, user_id: str) -> dict:
        """Cache user information for session management."""
        from superset import security_manager

        user = security_manager.find_user(user_id)
        if not user:
            return {}

        # Build user info payload
        user_info = {
            "user_id": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "email": user.email,
            "roles": [role.name for role in user.roles],
            "is_active": user.is_active,
        }

        return user_info

    def get_rls_filter_clause(self, **kwargs) -> dict:
        """Get RLS filter clause for the current user."""
        from flask_login import current_user

        if not current_user.is_authenticated:
            return {"ap": "1 = 0", "switch": "1 = 0"}

        # Get user's resource filter
        resource_filter = self._get_user_resource_filter(current_user.username)

        rls_filter_list = {}

        if resource_filter is None:
            logger.warning("get_rls_filter_clause -> resource filter is empty. Adding 1 = 0")
            rls_filter_list["ap"] = "1 = 0"
            rls_filter_list["switch"] = "1 = 0"
        else:
            if "networkNodes" in resource_filter:
                logger.debug("Adding ap clause from user resource filter")
                ap_clause_dict = self.__format_filter(resource_filter["networkNodes"])
                ap_clause = self.__format_clause(ap_clause_dict)
                rls_filter_list["ap"] = f"({ap_clause})"

            if "switchNodes" in resource_filter:
                logger.debug("Adding switch clause from user resource filter")
                switch_clause_dict = self.__format_filter(resource_filter["switchNodes"])
                switch_clause = self.__format_clause(switch_clause_dict)
                rls_filter_list["switch"] = f"({switch_clause})"

            if APP_VISIBILITY in resource_filter:
                rls_filter_list[APP_VISIBILITY] = resource_filter[APP_VISIBILITY]

        return rls_filter_list

    def get_redirect_path(self) -> str:
        """Get redirect path based on user roles."""
        from flask_login import current_user
        from mlisa.backend.utils.constants import REPORT_ONLY

        if hasattr(current_user, "roles"):
            roles = [role.name for role in current_user.roles]

            dashboard_data = self.get_tenant_dashboards()
            if not len(dashboard_data) and REPORT_ONLY not in roles:
                return "/superset/gallery/"

        return "/dashboard/list/"

    def _get_user_resource_filter(self, user_id: str) -> Optional[dict]:
        """Get resource filter for a specific user from MLISA cache."""
        try:
            # Use MLISA's own cache system instead of Superset's RLS
            cache_manager = RlsCacheManager()
            user_info = cache_manager.get_user_info(user_id)

            if user_info and 'resource_filter' in user_info:
                return user_info['resource_filter']

            return None
        except Exception as e:
            logger.error("Error getting resource filter for user %s: %s", user_id, e)
            return None

    def __format_filter(self, resource_filter) -> dict:
        """Format resource filter data into clause dictionary."""
        clause_dict = []
        if len(resource_filter):
            for nodes in resource_filter:
                filter_list = {}
                level = START_DOMAIN_LEVEL
                for node in nodes:
                    if len(filter_list) and node["type"] in filter_list:
                        if node["type"] == "domain":
                            filter_list[node["type"]] = (
                                filter_list[node["type"]]
                                + ", '"
                                + str(level)
                                + "||"
                                + node["name"].replace("'", "''")
                                + "'"
                            )
                            level += 1
                        elif node["type"] == "switch" or node["type"] == "apMac":
                            for val in node["list"]:
                                filter_list[node["type"]] = (
                                    filter_list[node["type"]] + ", '" + val + "'"
                                )
                        else:
                            filter_list[node["type"]] = (
                                filter_list[node["type"]]
                                + ", '"
                                + node["name"].replace("'", "''")
                                + "'"
                            )
                    else:
                        if node["type"] == "domain":
                            filter_list[node["type"]] = (
                                "'"
                                + str(level)
                                + "||"
                                + node["name"].replace("'", "''")
                                + "'"
                            )
                            level += 1
                        elif node["type"] == "switch" or node["type"] == "apMac":
                            for index, val in enumerate(node["list"]):
                                if not index:
                                    filter_list[node["type"]] = "'" + val + "'"
                                else:
                                    filter_list[node["type"]] = (
                                        filter_list[node["type"]]
                                        + ", '"
                                        + val
                                        + "'"
                                    )
                        else:
                            filter_list[node["type"]] = (
                                "'" + node["name"].replace("'", "''") + "'"
                            )
                if len(filter_list):
                    clause_dict.append(filter_list)
        return clause_dict

    def __format_clause(self, clause_dict) -> str:
        """Format clause dictionary into SQL WHERE clause string."""
        # Check for empty input
        if not clause_dict:
            return ""

        clause_list = ""
        # label mapping dict for filter keys
        key_label_dict = KEY_LABEL_DICT

        # Create a dictionary to group values by key
        grouped_clauses = {}
        for clauses in clause_dict:
            for key, value in clauses.items():
                if key not in grouped_clauses:
                    grouped_clauses[key] = []
                grouped_clauses[key].append(value)

        # Build the clause string from grouped clauses using AND
        for key, values in grouped_clauses.items():
            keyLabel = key_label_dict[key]
            clause = f'"{keyLabel}" in ({", ".join(sorted(set(values)))})'
            if clause_list:
                clause_list += " and " + clause  # Change to AND
            else:
                clause_list = clause

        logger.debug("Formatted RLS clause: %s", clause_list)
        return clause_list

    def __get_switch_tables(self, session, SqlaTable):
        """Get switch-related tables."""
        return (
            session.query(SqlaTable)
            .filter(SqlaTable.table_name.contains("switch"))
            .all()
        )

    def get_tables_list(self, session, exclude_tables, SqlaTable):
        """Get tables list excluding specified tables."""
        return (
            session.query(SqlaTable)
            .filter(SqlaTable.table_name.notin_(exclude_tables))
            .all()
        )

    def __rls_filter(self, resource_filter, user_id, session, tables, SqlaTable) -> dict:
        """Create RLS filter dictionary from resource filter."""
        role = self.create_role(user_id)

        rls_filter_dict, exclude_tables = {}, []
        tables_list = self.get_list_of_tables(session, SqlaTable)

        if "networkNodes" in resource_filter:
            logger.info("Adding ap clause from user resource filter")
            ap_clause_dict = self.__format_filter(resource_filter["networkNodes"])
            ap_clause = self.__format_clause(ap_clause_dict)
            rls_filter_dict["ap"] = {"clause": ap_clause, "role": role, "tables": tables_list["ap"]}

        if "switchNodes" in resource_filter:
            logger.info("Adding switch clause from user resource filter")
            switch_clause_dict = self.__format_filter(resource_filter["switchNodes"])
            switch_clause = self.__format_clause(switch_clause_dict)
            rls_filter_dict["switch"] = {"clause": switch_clause, "role": role, "tables": tables_list["switch"]}

        # Get controllerInventory table
        controller_sqla_table = tables_list["controller"]
        # Check if controllerInventory table is present
        if controller_sqla_table:
            # Add controllerInventory table to exclude tables list if networkNodes and switchNodes are empty in rg filter
            if (
                "networkNodes" in resource_filter
                and (not len(resource_filter["networkNodes"]))
            ) and (
                "switchNodes" in resource_filter
                and (not len(resource_filter["switchNodes"]))
            ):
                exclude_tables.append(controller_sqla_table.table_name)
            # Remove controllerInventory table from exclude tables list if networkNodes or switchNodes are not empty
            # in rg filter
            else:
                if controller_sqla_table.table_name in exclude_tables:
                    exclude_tables.remove(controller_sqla_table.table_name)

        # Get the table list to update datasource access for the role
        tables = self.get_tables_list(session, exclude_tables, SqlaTable)
        logger.debug("Datasources to be added to role(permissions): %s", tables)

        return rls_filter_dict

    def get_list_of_tables(self, session, SqlaTable):
        """Get organized list of tables by type."""
        # Get switch tables
        switch_sqla_tables = self.__get_switch_tables(session, SqlaTable)
        # Get controllerInventory table
        controller_sqla_table = (
            session.query(SqlaTable)
            .filter(SqlaTable.table_name.contains("controllerInventory"))
            .first()
        )
        exclude_switch_tables = []
        for table in switch_sqla_tables:
            exclude_switch_tables.append(table.table_name)
        if controller_sqla_table:
            switch_sqla_tables.append(controller_sqla_table)
        # Get AP tables
        ap_sqla_tables = self.get_tables_list(session, exclude_switch_tables, SqlaTable)
        return {'switch': switch_sqla_tables, 'ap': ap_sqla_tables, 'controller': controller_sqla_table}

    def add_default_rls_filter(self, role, session, SqlaTable):
        """Add default RLS filters for a role."""
        tables_list = self.get_list_of_tables(session, SqlaTable)
        self.add_rls_filter({"clause": '1 = 0', "role": role, "tables": tables_list["ap"]})
        self.add_rls_filter({"clause": '1 = 0', "role": role, "tables": tables_list["switch"]})

    def add_rls_filter(self, filter_value):
        """Add RLS filter to MLISA cache system (no longer using Superset RLS)."""
        # MLISA uses its own RLS system, not Superset's RowLevelSecurityFilter
        # This method is kept for API compatibility but no longer creates Superset RLS filters
        logger.debug("MLISA RLS filter handled by cache system, not Superset RLS")
        pass

    def get_rls_filter(self, role):
        """Get RLS filters for a role (MLISA custom implementation)."""
        # MLISA uses its own RLS system, not Superset's RowLevelSecurityFilter
        # Return empty list for API compatibility
        logger.debug("MLISA RLS filter handled by cache system, not Superset RLS")
        return []