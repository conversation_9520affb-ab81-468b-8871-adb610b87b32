import logging

logger = logging.getLogger(__name__)


class CliHelper:
    """CLI helper for managing MLISA roles and permissions setup."""

    def add_roles_and_perms(self):
        """Create MLISA UI roles and add permissions to them.

        Returns:
            True if successful, False otherwise
        """
        from mlisa.backend.security.rbac_helper import RBACHelper
        from mlisa.backend.utils.constants import ADMIN, NETWORK_ADMIN, REPORT_ONLY, GUEST

        try:
            rbac_helper = RBACHelper()

            ui_roles = [ADMIN, NETWORK_ADMIN, REPORT_ONLY, GUEST]
            logger.info("Creating MLISA UI Roles: %s", ui_roles)

            for role_name in ui_roles:
                try:
                    # Create the role
                    role = rbac_helper.create_role(role_name)
                    logger.info("Created/found role: %s", role_name)

                    # Add UI permissions to the role
                    self.add_ui_permissions_to_mlisa_roles(role)

                    logger.info("Successfully configured role: %s", role_name)

                except Exception as role_error:
                    logger.error("Error configuring role %s: %s", role_name, role_error)
                    return False

            logger.info("Completed creation and configuration of MLISA UI Roles")
            return True

        except Exception as e:
            logger.error("Error in add_roles_and_perms: %s", e)
            return False

    def add_ui_permissions_to_mlisa_roles(self, role):
        """Add UI permissions to a MLISA role.

        Args:
            role: The role object to add permissions to

        Returns:
            True if successful, False otherwise
        """
        try:
            from superset import security_manager
            from mlisa.backend.security.permissions import UIPermissions

            ui_perm = UIPermissions()
            permissions_list = ui_perm.permissions_list(role.name)

            if not permissions_list:
                logger.warning("No permissions found for role: %s", role.name)
                return True

            # Clear existing permissions for this role
            if role.permissions:
                logger.info("Clearing existing permissions for role: %s", role.name)
                for perm in list(role.permissions):
                    security_manager.del_permission_role(role, perm)

            # Add new permissions
            added_count = 0
            for (perm_name, view_name) in permissions_list:
                try:
                    pvm = security_manager.find_permission_view_menu(perm_name, view_name)
                    if pvm:
                        security_manager.add_permission_role(role, pvm)
                        added_count += 1
                    else:
                        logger.warning("Permission not found: %s on %s", perm_name, view_name)
                except Exception as perm_error:
                    logger.error("Error adding permission %s:%s to role %s: %s",
                               perm_name, view_name, role.name, perm_error)

            logger.info("Added %d permissions to role: %s", added_count, role.name)
            return True

        except Exception as e:
            logger.error("Error adding UI permissions to role %s: %s", role.name, e)
            return False
