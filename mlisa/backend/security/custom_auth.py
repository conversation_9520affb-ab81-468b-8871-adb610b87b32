import json
import logging

from flask import jsonify, make_response, request, session
from flask_appbuilder.security.sqla.models import User
from flask_appbuilder.security.views import AuthDBView, expose
from flask_login import current_user, login_user, logout_user

from superset import appbuilder
from superset.security import SupersetSecurityManager

from mlisa.backend.utils.config_utils import DEPLOYMENT_MODE
from mlisa.backend.utils.error_utils import json_error_response

logger = logging.getLogger(__name__)


class CustomAuthDBView(AuthDBView):
    """Custom authentication view for MLISA Data Studio."""

    def __create_or_update_user(self, user_info, rls_roles, deployment_mode) -> User:
        """Create or update user with proper role assignments."""
        from superset import security_manager
        from mlisa.backend.utils.constants import (
            ALTO_ROLE,
            EMAIL,
            FIRST_NAME,
            LAST_NAME,
            MLISA_ROLE,
            USER_ID,
        )
        from mlisa.backend.security.rbac_helper import <PERSON><PERSON><PERSON><PERSON>per

        ui_role = (
            user_info.get(MLISA_ROLE)
            if deployment_mode == "MLISA"
            else user_info.get(ALTO_ROLE)
        )

        user = security_manager.find_user(user_info.get(USER_ID))
        if user:  # Existing user, so let's update user
            user_roles = [security_manager.find_role(ui_role)]  # Ui Role
            for rls_role in rls_roles:
                user_roles.append(
                    security_manager.find_role(rls_role)
                )  # Other Roles used for RBAC

            user.roles = user_roles
            user.email = user_info.get(EMAIL)
            user.first_name = user_info.get(FIRST_NAME)
            user.last_name = user_info.get(LAST_NAME)

            logger.debug("Updating user: %s", user)
            security_manager.update_user(user)
        else:  # New user, so let's create user
            user_details = {
                "username": user_info.get(USER_ID),
                "uiRole": ui_role,
                "firstName": user_info.get(FIRST_NAME),
                "lastName": user_info.get(LAST_NAME),
                "email": user_info.get(EMAIL),
                "otherRoles": rls_roles,
            }
            logger.debug("Creating user: %s", user_details)
            users = RBACHelper().add_users([user_details])
            if users and len(users):
                user = users[0]
        return user

    def __create_or_update_roles(self, user_info):
        """Create or update roles and RLS filters for user."""
        from superset import security_manager
        from mlisa.backend.utils.constants import (
            IS_FRANCHISOR,
            OWN_TENANT_ID,
            OWN_TENANT_ID_PREFIX,
            TENANT_ID_PREFIX,
            TENANT_IDS,
            USER_ID,
        )
        from mlisa.backend.utils.rbac_adapter import get_user_resource_filter
        from mlisa.backend.security.rbac_helper import RBACHelper

        rbac_helper = RBACHelper()
        rls_roles = []
        own_tenant_id = user_info.get(OWN_TENANT_ID)
        tenant_ids = json.loads(user_info.get(TENANT_IDS))
        user_id = user_info.get(USER_ID)

        is_franchisor = user_info.get(IS_FRANCHISOR)
        logger.debug("__create_or_update_roles >> is_franchisor: %s", is_franchisor)

        if is_franchisor == "true":
            logger.debug(
                "Franchisor case: own_tenant_id: %s and tenant_ids: %s",
                own_tenant_id,
                tenant_ids,
            )
            rbac_helper.create_role(OWN_TENANT_ID_PREFIX + own_tenant_id)
            rls_roles.append(OWN_TENANT_ID_PREFIX + own_tenant_id)
            for tenant_id in tenant_ids:
                if own_tenant_id != tenant_id:
                    rbac_helper.create_role(TENANT_ID_PREFIX + tenant_id)
                    rls_roles.append(TENANT_ID_PREFIX + tenant_id)
        else:
            rbac_helper.create_role(OWN_TENANT_ID_PREFIX + tenant_ids[0])
            rls_roles.append(OWN_TENANT_ID_PREFIX + tenant_ids[0])

        rls_roles.append(user_id)
        role = security_manager.find_role(user_id)
        # We always fetch resource filter data to ensure we get the latest filter
        resource_filter = get_user_resource_filter(
            user_info.get(TENANT_IDS), user_id, is_franchisor
        )
        if role is not None:
            if not rbac_helper.update_rls_filter(resource_filter, user_id):
                return None
        else:
            if not rbac_helper.create_rls_filter(resource_filter, user_id):
                return None

        return rls_roles

    def __authenticate_user(self, user: User, redirect_url: str):
        """Authenticate user and create session."""
        from mlisa.backend.security.rbac_helper import RBACHelper

        # Always logout the current user to clear the session
        if current_user.is_authenticated:
            logger.info(
                "Logging out current user '%s %s (%s)' to prevent session hijacking. "
                "Creating new session for user '%s %s (%s)'",
                current_user.first_name,
                current_user.last_name,
                current_user.username,
                user.first_name,
                user.last_name,
                user.username,
            )
            logout_user()

        # MLISA uses custom RLS system - no need to check Superset's RLS filters
        logger.debug("Using MLISA custom RLS system for user %s", user.username)

        # Login the user and create a new session
        login_user(user, remember=False)

        # This is required for the PERMANENT_SESSION_LIFETIME to take effect
        session.permanent = True

        logger.info(
            "User '%s %s (%s)' logged in successfully. Redirecting to %s",
            user.first_name,
            user.last_name,
            user.username,
            redirect_url,
        )

        # Set locale to enable translation
        if request.args.get("locale"):
            from mlisa.backend.utils.locale_utils import get_superset_locale

            locale = get_superset_locale(request.args.get("locale"))
            logger.info("Setting locale to %s", locale)
            session["locale"] = locale

        user_info_payload = RBACHelper().cache_user_info(user_id=user.username)
        response_json = json.dumps(
            {"redirect_url": redirect_url, "user_info": user_info_payload}
        )
        logger.debug("Response: %s", response_json)

        response = make_response(response_json)
        # Clear the old "session" cookie in case it exists
        response.set_cookie("session", "", expires=0)

        return response

    def __handle_swuid_change(self, user: User, user_id: str):
        """Handle username change scenarios."""
        from superset import security_manager

        if user and user.username != user_id:
            logger.info(
                "Updating username from %s to %s for email %s",
                user.username,
                user_id,
                user.email,
            )
            user.username = user_id
            security_manager.update_user(user)

    # Removed duplicate __json_error_response method - use error_utils.json_error_response directly

    def create_user_and_roles(self, user_info, deployment_mode):
        """Create user and roles based on user information."""
        from superset import is_feature_enabled, security_manager
        from mlisa.backend.utils.constants import EMAIL, USER_ID

        # Handle username change case
        user_id = user_info.get(USER_ID)
        user = security_manager.find_user(email=user_info.get(EMAIL))
        self.__handle_swuid_change(user, user_id)

        logger.debug("Creating user roles and RLS filters for user_id: %s", user_id)

        # Create or Update Roles and RLS filters
        roles = self.__create_or_update_roles(user_info)
        if not roles:
            raise Exception("Unable to create or update roles!")

        # Create or Update User and associated roles
        user = self.__create_or_update_user(user_info, roles, deployment_mode)

        return [user, roles]

    @expose("/authenticate/", methods=["GET", "POST"])
    def mlisa_login(self):
        """MLISA authentication endpoint."""
        deployment_mode = DEPLOYMENT_MODE
        logger.info("Deployment Mode: %s", deployment_mode)
        from mlisa.backend.utils.auth_utils import validate_required_headers

        try:
            req_payload = (
                json.loads(request.data) if request.data and request.data else {}
            )
            logger.debug("Request Payload: %s", req_payload)
            user_data = req_payload.get("user") or {}

            # Validate required HTTP headers and extract the required data
            user_info = validate_required_headers(
                request.headers, user_data, deployment_mode
            )
            if user_info is None:
                return json_error_response(
                    err_msg="Missing required headers or data",
                    code="DATASTUDIO-10002",
                    status=400,
                    reason="Authentication request is missing required information",
                    suggestion="Please ensure all required headers and user data are provided",
                )

            # Let's create user and required roles, or update if already exists
            try:
                user, *_ = self.create_user_and_roles(user_info, deployment_mode)
            except Exception as role_error:
                logger.exception(role_error)
                return json_error_response(
                    err_msg=str(role_error),
                    code="DATASTUDIO-10003",
                    status=500,
                    reason="Failed to create or update user roles",
                    suggestion="Please contact your administrator",
                )

            # All is well, let's proceed with login
            try:
                return self.__authenticate_user(user, appbuilder.get_url_for_index)
            except Exception as auth_error:
                logger.exception(auth_error)
                return json_error_response(
                    err_msg=str(auth_error),
                    code="DATASTUDIO-10004",
                    status=500,
                    reason="User authentication failed",
                    suggestion="Please try again or contact your administrator",
                )
        except Exception as e:
            logger.exception(e)
            return json_error_response(
                err_msg=str(e),
                code="DATASTUDIO-10001",
                status=500,
                reason="Authentication failed",
                suggestion="Please check your credentials and try again",
            )

    @expose("/superset/log/", methods=["POST"])
    def log(self):
        """Override the default log endpoint with custom behavior."""
        return jsonify({"status": "OK"}), 200


class CustomSecurityManager(SupersetSecurityManager):
    """Custom security manager for MLISA Data Studio."""

    authdbview = CustomAuthDBView