import logging
from typing import Optional

from superset.security import SupersetSecurityManager
from mlisa.backend.security.custom_auth import CustomAuthDBView

logger = logging.getLogger(__name__)


class MLISASecurityManager(SupersetSecurityManager):
    """
    Custom Security Manager for MLISA Data Studio.

    This class extends Superset's security manager to integrate with MLISA's
    custom authentication and RBAC system.
    """

    # Use our custom authentication view
    authdbview = CustomAuthDBView

    def __init__(self, appbuilder):
        """Initialize MLISA Security Manager."""
        super().__init__(appbuilder)
        logger.info("MLISA Security Manager initialized")

    def get_user_roles(self, user=None):
        """Get user roles, including MLISA-specific roles."""
        from flask_login import current_user

        target_user = user if user else current_user
        if not target_user or not target_user.is_authenticated:
            return []

        # Get standard roles
        roles = super().get_user_roles(target_user)

        # Add MLISA-specific role processing if needed
        logger.debug("User %s has roles: %s", target_user.username, [r.name for r in roles])

        return roles

    def has_access(self, permission_name: str, view_name: str, user=None) -> bool:
        """Check if user has access to a specific permission."""
        from flask_login import current_user

        target_user = user if user else current_user
        if not target_user or not target_user.is_authenticated:
            return False

        # Check standard permissions first
        has_standard_access = super().has_access(permission_name, view_name, target_user)

        # Add MLISA-specific permission checks if needed
        logger.debug("Permission check for user %s: %s on %s = %s",
                    target_user.username, permission_name, view_name, has_standard_access)

        return has_standard_access

    def get_user_menu_access(self, menu_names=None):
        """Get menu access for current user based on MLISA roles."""
        from flask_login import current_user

        if not current_user.is_authenticated:
            return []

        # Get standard menu access
        menu_access = super().get_user_menu_access(menu_names)

        # Add MLISA-specific menu filtering based on roles
        user_roles = [role.name for role in current_user.roles]
        logger.debug("User %s menu access filtered by roles: %s",
                    current_user.username, user_roles)

        return menu_access

    def validate_user_session(self, user) -> bool:
        """Validate user session for MLISA-specific requirements."""
        if not user or not user.is_authenticated:
            return False

        # Add MLISA-specific session validation
        from mlisa.backend.security.rbac_helper import RBACHelper
        from superset import is_feature_enabled

        try:
            # MLISA uses custom RLS system - no need to check Superset's RLS filters
            logger.debug("Session validation passed for user %s (using MLISA custom RLS)", user.username)
            return True

        except Exception as e:
            logger.error("Session validation error for user %s: %s", user.username, e)
            return False

    def get_user_tenant_context(self, user=None) -> Optional[dict]:
        """Get tenant context for the user."""
        from flask_login import current_user
        from mlisa.backend.security.rbac_helper import RBACHelper

        target_user = user if user else current_user
        if not target_user or not target_user.is_authenticated:
            return None

        try:
            rbac_helper = RBACHelper()
            tenant_data = rbac_helper.get_current_user_tenant_id()

            return {
                "user_id": target_user.username,
                "tenant_ids": tenant_data[0] if tenant_data else [],
                "is_franchisor": tenant_data[1] if len(tenant_data) > 1 else "false"
            }

        except Exception as e:
            logger.error("Error getting tenant context for user %s: %s",
                        target_user.username, e)
            return None

    def cleanup_user_session(self, user_id: str) -> bool:
        """Cleanup user session data and cache."""
        try:
            from mlisa.backend.utils.cache_manager import RlsCacheManager, SessionCacheManager

            # Clean up RLS cache
            rls_cache = RlsCacheManager(user_id)
            rls_cache.delete_rls_from_cache()

            # Clean up session cache
            session_cache = SessionCacheManager(user_id)
            session_cache.delete_session_data()

            logger.info("Cleaned up session data for user %s", user_id)
            return True

        except Exception as e:
            logger.error("Error cleaning up session for user %s: %s", user_id, e)
            return False

    def sync_user_permissions(self, user, force_update=False) -> bool:
        """Sync user permissions with MLISA RBAC system."""
        try:
            from mlisa.backend.security.rbac_helper import RBACHelper
            from mlisa.backend.security.permissions import UIPermissions

            rbac_helper = RBACHelper()
            ui_permissions = UIPermissions()

            # Get user's primary role
            if not user.roles:
                logger.warning("User %s has no roles assigned", user.username)
                return False

            primary_role = user.roles[0]  # First role is typically the UI role

            # Validate role permissions
            if not ui_permissions.validate_role_permissions(primary_role.name):
                logger.error("Invalid permissions for role %s", primary_role.name)
                return False

            # Update RLS filters if needed
            if force_update:
                # Force refresh of RLS filters
                # This would typically be called during login or role changes
                pass

            logger.info("Synced permissions for user %s with role %s",
                       user.username, primary_role.name)
            return True

        except Exception as e:
            logger.error("Error syncing permissions for user %s: %s", user.username, e)
            return False


# Export the security manager class
__all__ = ['MLISASecurityManager']