import logging

logger = logging.getLogger(__name__)

"""
Permission definitions for MLISA Data Studio roles.
This module defines the permissions for different user roles in the system.
"""


class UIPermissions:
    """Manages UI permissions for different MLISA roles."""

    def permissions_list(self, role):
        """Get permissions list for a specific role.

        Args:
            role: The role name (admin, network-admin, report-only, guest)

        Returns:
            Set of permission tuples for the role
        """
        from mlisa.backend.utils.constants import ADMIN, GUEST, NETWORK_ADMIN, REPORT_ONLY

        read_only_perms = {
            ("can_read", "SavedQuery"),
            ("can_read", "Chart"),
            ("can_read", "CssTemplate"),
            ("can_read", "Annotation"),
            ("can_read", "Dataset"),
            ("can_read", "Dashboard"),
            ("can_recent_activity", "Superset"),
            ("can_stop_query", "Superset"),
            ("can_dashboard", "Superset"),
            ("can_queries", "Superset"),
            ("menu_access", "Home"),
            ("menu_access", "Dashboards"),
            ("menu_access", "Charts"),
            ("can_explore_json", "Superset"),
            ("can_explore", "Superset"),
            ("can_get", "Datasource"),
            ("can_favstar", "Superset"),
            ("can_fave_slices", "Superset"),
            ("can_fave_dashboards", "Superset"),
            ("can_fave_dashboards_by_username", "Superset"),
            ("can_filter", "Superset"),
            ("can_csv", "Superset"),
            ("can_time_range", "Api"),
            ("can_read", "DashboardFilterStateRestApi"),
            (
                "can_write",
                "DashboardFilterStateRestApi",
            ),  # Client Export PDF requires API call to fetch filters
        }  # 25 perms

        read_write_perms = read_only_perms.union(
            {
                ("can_write", "Chart"),
                ("can_write", "Annotation"),
                ("can_write", "Dashboard"),
                ("can_publish", "Superset"),
                ("can_slice", "Superset"),
                ("can_created_dashboards", "Superset"),
                ("can_add_slices", "Superset"),
                ("can_select_star", "Superset"),
                ("can_copy_dash", "Superset"),
                ("can_save_dash", "Superset"),
                ("can_created_slices", "Superset"),
                ("can_results", "Superset"),
                ("can_put", "TabStateView"),
                ("can_post", "TabStateView"),
                ("can_get", "TabStateView"),
                ("can_tagged_objects", "TagView"),
                ("can_suggestions", "TagView"),
                ("can_post", "TagView"),
                ("can_get", "TagView"),
                ("menu_access", "Charts"),
                ("menu_access", "Gallery"),
                ("all_query_access", "all_query_access"),
                ("menu_access", "Import Dashboards"),
                ("menu_access", "Manage"),
                ("can_share_dashboard", "Superset"),
                ("can_share_chart", "Superset"),
                ("can_import_dashboards", "Superset"),
                ("can_read", "ReportSchedule"),
                (
                    "menu_access",
                    "Schedules",
                ),  # Menu name in sync with menu name given here superset/initialization/__init__.py
                (
                    "can_write",
                    "ReportSchedule",
                ),  # Needs to be enabled when (menu_access, Manage) is enabled, for more info:superset-frontend/src/explore/components/ExploreChartHeader.jsx
                ("can_write", "ExploreFormDataRestApi"),
                ("can_read", "ExploreFormDataRestApi"),
                ("can_export", "Dashboard"),
                ("can_export", "Chart"),
            }
        )  # 35 additional perms = 60 total

        guest_perms = read_only_perms.union(
            {
                ("can_read", "ReportSchedule"),
                ("can_write", "ReportSchedule"),
            }
        )  # 27 total perms

        roles_permission_mapping = {
            ADMIN: read_write_perms,
            NETWORK_ADMIN: read_write_perms,
            REPORT_ONLY: read_only_perms,
            GUEST: guest_perms,
        }

        permissions = roles_permission_mapping.get(role)
        if permissions:
            logger.debug("Role %s has %d permissions", role, len(permissions))
        else:
            logger.warning("No permissions found for role: %s", role)

        return permissions

    def get_role_permissions_count(self) -> dict:
        """Get count of permissions for each role.

        Returns:
            Dictionary mapping role names to permission counts
        """
        from mlisa.backend.utils.constants import ADMIN, GUEST, NETWORK_ADMIN, REPORT_ONLY

        roles = [ADMIN, NETWORK_ADMIN, REPORT_ONLY, GUEST]
        counts = {}

        for role in roles:
            perms = self.permissions_list(role)
            counts[role] = len(perms) if perms else 0

        return counts

    def validate_role_permissions(self, role: str) -> bool:
        """Validate that a role has the minimum required permissions.

        Args:
            role: The role name to validate

        Returns:
            True if role has valid permissions, False otherwise
        """
        perms = self.permissions_list(role)

        if not perms:
            logger.error("Role %s has no permissions defined", role)
            return False

        # Check for minimum required permissions
        required_base_perms = {
            ("menu_access", "Home"),
            ("menu_access", "Dashboards"),
            ("can_read", "Dashboard"),
        }

        if not required_base_perms.issubset(perms):
            missing = required_base_perms - perms
            logger.error("Role %s is missing required permissions: %s", role, missing)
            return False

        logger.info("Role %s has valid permissions", role)
        return True

    def get_permission_diff(self, role1: str, role2: str) -> dict:
        """Compare permissions between two roles.

        Args:
            role1: First role name
            role2: Second role name

        Returns:
            Dictionary with permission differences
        """
        perms1 = self.permissions_list(role1) or set()
        perms2 = self.permissions_list(role2) or set()

        return {
            "role1_only": perms1 - perms2,
            "role2_only": perms2 - perms1,
            "common": perms1 & perms2,
            "total_diff": len(perms1 - perms2) + len(perms2 - perms1)
        }