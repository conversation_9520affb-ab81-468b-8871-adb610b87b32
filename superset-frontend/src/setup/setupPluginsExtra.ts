/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// MLISA Message Listener - handles cross-origin communication for embedded dashboards
// This is kept minimal and only activated when DEPLOYMENT_MODE=MLISA
function setupMlisaMessageListener() {
  // Dynamic import to get setupClient when needed
  const getSetupClient = () => import('./setupClient').then(m => m.default);

  window.addEventListener('message', event => {
    // To ensure we only listen to messages from localhost only
    if (
      event.origin === 'http://localhost:5173' ||
      event.origin === 'http://127.0.0.1:5173'
    ) {
      const { type } = event.data;

      // Handle DataStudio user info message
      if (type === 'dataStudioUserInfo' && event.data.userInfo) {
        const { userInfo } = event.data;
        window.sessionStorage.setItem('user_info', userInfo);
        setTimeout(() => {
          // Need to wait a bit for the user info to be loaded
          getSetupClient().then(setupClient => setupClient());
        }, 100);
      }

      // Handle Embedded Dashboard user info message
      if (type === 'embeddedUserInfo' && event.data.userInfo) {
        const { userInfo } = event.data;

        try {
          window.sessionStorage.setItem('user_info', userInfo);

          // Send confirmation back to parent
          window.parent.postMessage(
            {
              type: 'embeddedUserInfoReceived',
              success: true,
              timestamp: Date.now(),
              domain: window.location.origin,
            },
            event.origin,
          );
        } catch (error) {
          // Silent error handling for embedded context
        }
        // For embedded dashboards, don't call setupClient as it uses setupGuestClient
      }
    }

    if (event.data?.type === 'refreshToken') {
      getSetupClient().then(setupClient => setupClient());
    }
  });
}

// For individual deployments to add custom overrides
export default function setupPluginsExtra() {
  // MLISA-specific setup for embedded dashboard communication
  if (process.env.DEPLOYMENT_MODE === 'MLISA') {
    setupMlisaMessageListener();
  }
}
