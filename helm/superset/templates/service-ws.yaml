{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}

{{- if .Values.supersetWebsockets.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: "{{ template "superset.fullname" . }}-ws"
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "superset.name" . }}
    chart: {{ template "superset.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  {{- with .Values.supersetWebsockets.service.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.supersetWebsockets.service.type }}
  ports:
    - port: {{ .Values.supersetWebsockets.service.port }}
      targetPort: ws
      protocol: TCP
      name: ws
      {{- if and (or (eq .Values.supersetWebsockets.service.type "NodePort") (eq .Values.supersetWebsockets.service.type "LoadBalancer")) (not (empty .Values.supersetWebsockets.service.nodePort.http)) }}
      nodePort: {{ .Values.supersetWebsockets.service.nodePort.http }}
      {{- end }}
  selector:
    app: "{{ template "superset.name" . }}-ws"
    release: {{ .Release.Name }}
  {{- if .Values.supersetWebsockets.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.supersetWebsockets.service.loadBalancerIP }}
  {{- end }}
{{- end }}
