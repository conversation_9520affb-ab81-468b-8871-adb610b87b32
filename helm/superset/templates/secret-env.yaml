{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}
{{- if .Values.secretEnv.create -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "superset.fullname" . }}-env
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "superset.fullname" . }}
    chart: {{ template "superset.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: Opaque
stringData:
    REDIS_HOST: {{ tpl .Values.supersetNode.connections.redis_host . | quote }}
    REDIS_USER: {{ .Values.supersetNode.connections.redis_user | quote }}
    {{- if .Values.supersetNode.connections.redis_password }}
    REDIS_PASSWORD: {{ .Values.supersetNode.connections.redis_password | quote }}
    {{- end }}
    REDIS_PORT: {{ .Values.supersetNode.connections.redis_port | quote }}
    REDIS_PROTO: {{ if .Values.supersetNode.connections.redis_ssl.enabled }}"rediss"{{ else }}"redis"{{ end }}
    REDIS_DB: {{ .Values.supersetNode.connections.redis_cache_db | quote }}
    REDIS_CELERY_DB: {{ .Values.supersetNode.connections.redis_celery_db | quote }}
    {{- if .Values.supersetNode.connections.redis_ssl.enabled }}
    REDIS_SSL_CERT_REQS: {{ .Values.supersetNode.connections.redis_ssl.ssl_cert_reqs | default "CERT_NONE" | quote }}
    {{- end }}
    DB_HOST: {{ tpl .Values.supersetNode.connections.db_host . | quote }}
    DB_PORT: {{ .Values.supersetNode.connections.db_port | quote }}
    DB_USER: {{ .Values.supersetNode.connections.db_user | quote }}
    DB_PASS: {{ .Values.supersetNode.connections.db_pass | quote }}
    DB_NAME: {{ .Values.supersetNode.connections.db_name | quote }}
    {{- if .Values.extraSecretEnv }}
    {{- range $key, $value := .Values.extraSecretEnv }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
    {{- end }}
{{- end }}
