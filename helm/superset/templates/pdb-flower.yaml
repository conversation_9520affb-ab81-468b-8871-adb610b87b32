{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}

{{- with .Values.supersetCeleryFlower.podDisruptionBudget }}
{{- if .enabled -}}
{{- if and .minAvailable .maxUnavailable }}
{{- fail "Only one of minAvailable or maxUnavailable should be set" }}
{{- end}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "superset.fullname" $ }}-flower-pdb
  labels:
    app: {{ template "superset.name" $ }}-flower
    chart: {{ template "superset.chart" $ }}
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
spec:
{{- if .minAvailable }}
  minAvailable: {{ .minAvailable }}
{{- end }}
{{- if .maxUnavailable }}
  maxUnavailable: {{ .maxUnavailable }}
{{- end }}
  selector:
    matchLabels:
    {{- include "supersetCeleryFlower.selectorLabels" $ | nindent 6 }}
{{- end }}
{{- end }}
