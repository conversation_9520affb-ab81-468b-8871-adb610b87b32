{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}

{{- if .Values.supersetNode.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "superset.fullname" . }}-hpa
  labels:
    app: {{ template "superset.name" . }}
    chart: {{ template "superset.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "superset.fullname" . }}
  minReplicas: {{ .Values.supersetNode.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.supersetNode.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.supersetNode.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.supersetNode.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.supersetNode.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.supersetNode.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
