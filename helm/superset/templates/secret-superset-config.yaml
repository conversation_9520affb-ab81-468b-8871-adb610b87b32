{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}

apiVersion: v1
kind: Secret
metadata:
  name: {{ template "superset.fullname" . }}-config
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "superset.fullname" . }}
    chart: {{ template "superset.chart" . }}
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: Opaque
stringData:
  superset_config.py: |
    {{- include "superset-config" . | nindent 4 }}
  superset_init.sh: |
    {{- tpl .Values.init.initscript . | nindent 4 }}
  superset_bootstrap.sh: |
    {{- tpl .Values.bootstrapScript . | nindent 4 }}

  {{- if .Values.extraSecrets }}
  {{- range $path, $config := .Values.extraSecrets }}
  {{ $path }}: |
    {{- tpl $config $ | nindent 4 -}}
  {{- end -}}
  {{- end -}}
