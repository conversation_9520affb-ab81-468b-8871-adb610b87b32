{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}

{{- if .Values.supersetCeleryFlower.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "superset.fullname" . }}-flower
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "superset.name" . }}-flower
    chart: {{ template "superset.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  {{- if .Values.supersetCeleryFlower.deploymentAnnotations }}
  annotations: {{- toYaml .Values.supersetCeleryFlower.deploymentAnnotations | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.supersetCeleryFlower.replicaCount }}
  selector:
    matchLabels:
    {{- include "supersetCeleryFlower.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include "superset-config" . | sha256sum }}
        checksum/secrets: {{ tpl (toJson .Values.extraSecretEnv) . | sha256sum }}
        {{- if .Values.supersetCeleryFlower.podAnnotations }}
          {{- toYaml .Values.supersetCeleryFlower.podAnnotations | nindent 8 }}
        {{- end }}
      labels:
        app: "{{ template "superset.name" . }}-flower"
        release: {{ .Release.Name }}
        {{- if .Values.supersetCeleryFlower.podLabels }}
          {{- toYaml .Values.supersetCeleryFlower.podLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- if or (.Values.serviceAccount.create) (.Values.serviceAccountName) }}
      serviceAccountName: {{ template "superset.serviceAccountName" . }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.runAsUser }}
        {{- if .Values.supersetCeleryFlower.podSecurityContext }}
          {{- toYaml .Values.supersetCeleryFlower.podSecurityContext | nindent 8 }}
        {{- end }}
      {{- if .Values.supersetCeleryFlower.initContainers }}
      initContainers: {{-  tpl (toYaml .Values.supersetCeleryFlower.initContainers) . | nindent 6 }}
      {{- end }}
      {{- with .Values.hostAliases }}
      hostAliases: {{- toYaml . | nindent 6 }}
      {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-flower"
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.supersetCeleryFlower.containerSecurityContext }}
          securityContext: {{- toYaml .Values.supersetCeleryFlower.containerSecurityContext | nindent 12 }}
          {{- end }}
          command: {{  tpl (toJson .Values.supersetCeleryFlower.command) . }}
          env:
            {{- range $key, $value := .Values.extraEnv }}
            - name: {{ $key | quote}}
              value: {{ $value | quote }}
            {{- end }}
            {{- if .Values.extraEnvRaw }}
              {{- toYaml .Values.extraEnvRaw | nindent 12 }}
            {{- end }}
          envFrom:
            - secretRef:
                name: {{ tpl .Values.envFromSecret . | quote }}
            {{- range .Values.envFromSecrets }}
            - secretRef:
                name: {{ tpl . $ | quote }}
            {{- end }}
          ports:
            - name: flower
              containerPort: 5555
              protocol: TCP
          volumeMounts:
            - name: superset-config
              mountPath: {{ .Values.configMountPath | quote }}
              readOnly: true
            {{- with .Values.extraVolumeMounts }}
              {{- tpl (toYaml .) $ | nindent 12 -}}
            {{- end }}
          {{- if .Values.supersetCeleryFlower.startupProbe }}
          startupProbe: {{- .Values.supersetCeleryFlower.startupProbe | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.supersetCeleryFlower.readinessProbe }}
          readinessProbe: {{- .Values.supersetCeleryFlower.readinessProbe | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.supersetCeleryFlower.livenessProbe }}
          livenessProbe: {{- .Values.supersetCeleryFlower.livenessProbe | toYaml | nindent 12 }}
          {{- end }}
          resources:
            {{- if .Values.supersetCeleryFlower.resources }}
              {{- toYaml .Values.supersetCeleryFlower.resources | nindent 12 }}
            {{- else }}
              {{- toYaml .Values.resources | nindent 12 }}
            {{- end }}
        {{- if .Values.supersetCeleryFlower.extraContainers }}
            {{- toYaml .Values.supersetCeleryFlower.extraContainers | nindent 8 }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector: {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if or .Values.affinity .Values.supersetCeleryFlower.affinity }}
      affinity:
        {{- with .Values.affinity }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.supersetCeleryFlower.affinity }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- if .Values.supersetCeleryFlower.priorityClassName }}
      priorityClassName: {{ .Values.supersetCeleryFlower.priorityClassName }}
      {{- end }}
      {{- if or .Values.topologySpreadConstraints .Values.supersetCeleryFlower.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- with .Values.topologySpreadConstraints }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.supersetCeleryFlower.topologySpreadConstraints }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations: {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets: {{- toYaml .Values.imagePullSecrets | nindent 8 }}
      {{- end }}
      volumes:
        - name: superset-config
          secret:
            secretName: {{ tpl .Values.configFromSecret . }}
        {{- with .Values.extraVolumes }}
          {{- tpl (toYaml .) $ | nindent 8 -}}
        {{- end }}
{{- end -}}
