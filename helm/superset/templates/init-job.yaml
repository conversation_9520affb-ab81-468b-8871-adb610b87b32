{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}

{{- if .Values.init.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ template "superset.fullname" . }}-init-db
  namespace: {{ .Release.Namespace }}
  {{- if .Values.init.jobAnnotations }}
  annotations: {{- toYaml .Values.init.jobAnnotations | nindent 4 }}
  {{- end }}
spec:
  template:
    metadata:
      name: {{ template "superset.fullname" . }}-init-db
      {{- if .Values.init.podAnnotations }}
      annotations: {{- toYaml .Values.init.podAnnotations | nindent 8 }}
      {{- end }}
    spec:
      {{- if or (.Values.serviceAccount.create) (.Values.serviceAccountName) }}
      serviceAccountName: {{ template "superset.serviceAccountName" . }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.runAsUser }}
        {{- if .Values.init.podSecurityContext }}
          {{- toYaml .Values.init.podSecurityContext | nindent 8 }}
        {{- end }}
      {{- if .Values.init.initContainers }}
      initContainers: {{-  tpl (toYaml .Values.init.initContainers) . | nindent 6 }}
      {{- end }}
      containers:
      - name: {{ template "superset.name" . }}-init-db
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        {{- if or .Values.extraEnv .Values.extraEnvRaw }}
        env:
          {{- range $key, $value := .Values.extraEnv }}
          - name: {{ $key | quote }}
            value: {{ $value | quote }}
          {{- end }}
          {{- if .Values.extraEnvRaw }}
            {{- toYaml .Values.extraEnvRaw | nindent 10 }}
          {{- end }}
        {{- end }}
        envFrom:
          - secretRef:
              name: {{ tpl .Values.envFromSecret . }}
          {{- range .Values.envFromSecrets }}
          - secretRef:
              name: {{ tpl . $ | quote }}
          {{- end }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- if .Values.init.containerSecurityContext }}
        securityContext: {{- toYaml .Values.init.containerSecurityContext | nindent 12 }}
        {{- end }}
        volumeMounts:
          - name: superset-config
            mountPath: {{ .Values.configMountPath | quote }}
            readOnly: true
          {{- if .Values.extraConfigs }}
          - name: superset-extra-config
            mountPath: {{ .Values.extraConfigMountPath | quote }}
            readOnly: true
          {{- end }}
          {{- with .Values.extraVolumeMounts }}
            {{- tpl (toYaml .) $ | nindent 10 -}}
          {{- end }}
        command: {{  tpl (toJson .Values.init.command) . }}
        resources: {{- toYaml .Values.init.resources | nindent 10 }}
      {{- if .Values.init.extraContainers }}
          {{- toYaml .Values.init.extraContainers | nindent 6 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector: {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if or .Values.affinity .Values.init.affinity }}
      affinity:
        {{- with .Values.affinity }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.init.affinity }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- if .Values.init.priorityClassName }}
      priorityClassName: {{ .Values.init.priorityClassName }}
      {{- end }}
      {{- if or .Values.topologySpreadConstraints .Values.init.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- with .Values.topologySpreadConstraints }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.init.topologySpreadConstraints }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations: {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets: {{- toYaml .Values.imagePullSecrets | nindent 8 }}
      {{- end }}
      volumes:
        - name: superset-config
          secret:
            secretName: {{ tpl .Values.configFromSecret . }}
        {{- if .Values.extraConfigs }}
        - name: superset-extra-config
          configMap:
            name: {{ template "superset.fullname" . }}-extra-config
        {{- end }}
        {{- with .Values.extraVolumes }}
          {{- tpl (toYaml .) $ | nindent 8 -}}
        {{- end }}
      restartPolicy: Never
{{- end }}
