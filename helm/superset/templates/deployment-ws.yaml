{{/*

 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

*/}}

{{- if .Values.supersetWebsockets.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ template "superset.fullname" . }}-ws"
  namespace: {{ .Release.Namespace }}
  labels:
    app: "{{ template "superset.name" . }}-ws"
    chart: {{ template "superset.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  {{- if .Values.supersetWebsockets.deploymentAnnotations }}
  annotations: {{- toYaml .Values.supersetWebsockets.deploymentAnnotations | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.supersetWebsockets.replicaCount }}
  selector:
    matchLabels:
    {{- include "supersetWebsockets.selectorLabels" . | nindent 6 }}
  {{- if .Values.supersetWebsockets.strategy }}
  strategy: {{- toYaml .Values.supersetWebsockets.strategy | nindent 4 }}
  {{- end }}
  template:
    metadata:
      annotations:
        checksum/wsconfig: {{ tpl (toJson .Values.supersetWebsockets.config) . | sha256sum }}
        checksum/secrets: {{ tpl (toJson .Values.extraSecretEnv) . | sha256sum }}
        {{- if .Values.supersetWebsockets.podAnnotations }}
          {{- toYaml .Values.supersetWebsockets.podAnnotations | nindent 8 }}
        {{- end }}
      labels:
        app: "{{ template "superset.name" . }}-ws"
        release: {{ .Release.Name }}
        {{- if .Values.supersetWebsockets.podLabels }}
          {{- toYaml .Values.supersetWebsockets.podLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- if or (.Values.serviceAccount.create) (.Values.serviceAccountName) }}
      serviceAccountName: {{ template "superset.serviceAccountName" . }}
      {{- end }}
      securityContext:
        runAsUser: {{ .Values.runAsUser }}
        {{- if .Values.supersetWebsockets.podSecurityContext }}
          {{- toYaml .Values.supersetWebsockets.podSecurityContext | nindent 8 }}
        {{- end }}
      {{- with .Values.hostAliases }}
      hostAliases: {{- toYaml . | nindent 6 }}
      {{- end }}
      containers:
        - name: "{{ .Chart.Name }}-ws"
          image: "{{ .Values.supersetWebsockets.image.repository }}:{{ .Values.supersetWebsockets.image.tag }}"
          imagePullPolicy: {{ .Values.supersetWebsockets.image.pullPolicy }}
          {{- if .Values.supersetWebsockets.containerSecurityContext }}
          securityContext: {{- toYaml .Values.supersetWebsockets.containerSecurityContext | nindent 12 }}
          {{- end }}
          command: {{  tpl (toJson .Values.supersetWebsockets.command) . }}
          # Passing all the envs is a bit blunt... we only need a few (see https://github.com/apache/superset/blob/master/superset-websocket/src/config.ts)...
          env:
            {{- range $key, $value := .Values.extraEnv }}
            - name: {{ $key | quote}}
              value: {{ $value | quote }}
            {{- end }}
            {{- if .Values.extraEnvRaw }}
              {{- toYaml .Values.extraEnvRaw | nindent 12 }}
            {{- end }}
          envFrom:
            - secretRef:
                name: {{ tpl .Values.envFromSecret . | quote }}
            {{- range .Values.envFromSecrets }}
            - secretRef:
                name: {{ tpl . $ | quote }}
            {{- end }}
          ports:
            - name: ws
              containerPort: {{ .Values.supersetWebsockets.config.port }}
              protocol: TCP
          volumeMounts:
            - name: superset-ws-config
              mountPath: /home/<USER>/config.json
              subPath: config.json
              readOnly: true
          resources:
            {{- if .Values.supersetWebsockets.resources }}
              {{- toYaml .Values.supersetWebsockets.resources | nindent 12 }}
            {{- else }}
              {{- toYaml .Values.resources | nindent 12 }}
            {{- end }}
          {{- if .Values.supersetWebsockets.startupProbe }}
          startupProbe: {{- .Values.supersetWebsockets.startupProbe | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.supersetWebsockets.readinessProbe }}
          readinessProbe: {{- .Values.supersetWebsockets.readinessProbe | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.supersetWebsockets.livenessProbe }}
          livenessProbe: {{- .Values.supersetWebsockets.livenessProbe | toYaml | nindent 12 }}
          {{- end }}
        {{- if .Values.supersetWebsockets.extraContainers }}
            {{- toYaml .Values.supersetWebsockets.extraContainers | nindent 8 }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector: {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if or .Values.affinity .Values.supersetWebsockets.affinity }}
      affinity:
        {{- with .Values.affinity }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.supersetWebsockets.affinity }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- if .Values.supersetWebsockets.priorityClassName }}
      priorityClassName: {{ .Values.supersetWebsockets.priorityClassName }}
      {{- end }}
      {{- if or .Values.topologySpreadConstraints .Values.supersetWebsockets.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- with .Values.topologySpreadConstraints }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.supersetWebsockets.topologySpreadConstraints }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:  {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets: {{- toYaml .Values.imagePullSecrets | nindent 8 }}
      {{- end }}
      volumes:
        - name: superset-ws-config
          secret:
            secretName: "{{ template "superset.fullname" . }}-ws-config"
{{- end }}
