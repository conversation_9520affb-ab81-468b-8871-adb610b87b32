# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
dashboard_title: Sales Dashboard
description: null
css: ""
slug: null
certified_by: ""
certification_details: ""
published: true
uuid: 04f79081-fb49-7bac-7f14-cc76cd2ad93b
position:
  CHART-1NOOLm5YPs:
    children: []
    id: CHART-1NOOLm5YPs
    meta:
      chartId: 2805
      height: 25
      sliceName: Total Items Sold
      sliceNameOverride: Total Products Sold
      uuid: c3d643cd-fd6f-4659-a5b7-59402487a8d0
      width: 2
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-Tyv02UA_6W
      - COLUMN-8Rp54B6ikC
    type: CHART
  CHART-AYpv8gFi_q:
    children: []
    id: CHART-AYpv8gFi_q
    meta:
      chartId: 2810
      height: 70
      sliceName: Number of Deals (for each Combination)
      uuid: bd20fc69-dd51-46c1-99b5-09e37a434bf1
      width: 6
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-4fthLQmdX
      - ROW-0l1WcDzW3
    type: CHART
  CHART-KKT9BsnUst:
    children: []
    id: CHART-KKT9BsnUst
    meta:
      chartId: 2806
      height: 59
      sliceName: Quarterly Sales (By Product Line)
      sliceNameOverride: Quarterly Revenue (By Product Line)
      uuid: db9609e4-9b78-4a32-87a7-4d9e19d51cd8
      width: 7
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-oAtmu5grZ
    type: CHART
  CHART-OJ9aWDmn1q:
    children: []
    id: CHART-OJ9aWDmn1q
    meta:
      chartId: 2808
      height: 70
      sliceName: Proportion of Revenue by Product Line
      sliceNameOverride: Proportion of Monthly Revenue by Product Line
      uuid: 08aff161-f60c-4cb3-a225-dc9b1140d2e3
      width: 6
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-4fthLQmdX
      - ROW-0l1WcDzW3
    type: CHART
  CHART-YFg-9wHE7s:
    children: []
    id: CHART-YFg-9wHE7s
    meta:
      chartId: 2811
      height: 49
      sliceName: Seasonality of Revenue (per Product Line)
      uuid: cf0da099-b3ab-4d94-ab62-cf353ac3c611
      width: 6
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-4fthLQmdX
      - ROW-E7MDSGfnm
    type: CHART
  CHART-_LMKI0D3tj:
    children: []
    id: CHART-_LMKI0D3tj
    meta:
      chartId: 2809
      height: 49
      sliceName: Revenue by Deal Size
      sliceNameOverride: Monthly Revenue by Deal SIze
      uuid: f065a533-2e13-42b9-bd19-801a21700dff
      width: 6
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-4fthLQmdX
      - ROW-E7MDSGfnm
    type: CHART
  CHART-id4RGv80N-:
    children: []
    id: CHART-id4RGv80N-
    meta:
      chartId: 2807
      height: 59
      sliceName: Total Items Sold (By Product Line)
      sliceNameOverride: Total Products Sold (By Product Line)
      uuid: b8b7ca30-6291-44b0-bc64-ba42e2892b86
      width: 2
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-oAtmu5grZ
      - COLUMN-G6_2DvG8aK
    type: CHART
  CHART-j24u8ve41b:
    children: []
    id: CHART-j24u8ve41b
    meta:
      chartId: 670
      height: 59
      sliceName: Overall Sales (By Product Line)
      sliceNameOverride: Total Revenue (By Product Line)
      uuid: 09c497e0-f442-1121-c9e7-671e37750424
      width: 3
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-oAtmu5grZ
    type: CHART
  CHART-lFanAaYKBK:
    children: []
    id: CHART-lFanAaYKBK
    meta:
      chartId: 2804
      height: 26
      sliceName: Total Revenue
      uuid: 7b12a243-88e0-4dc5-ac33-9a840bb0ac5a
      width: 3
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-Tyv02UA_6W
      - COLUMN-8Rp54B6ikC
    type: CHART
  CHART-vomBOiI7U9:
    children: []
    id: CHART-vomBOiI7U9
    meta:
      chartId: 668
      height: 53
      sliceName: Quarterly Sales
      sliceNameOverride: Quarterly Revenue
      uuid: 692aca26-a526-85db-c94c-411c91cc1077
      width: 7
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-Tyv02UA_6W
    type: CHART
  COLUMN-8Rp54B6ikC:
    children:
      - CHART-lFanAaYKBK
      - CHART-1NOOLm5YPs
    id: COLUMN-8Rp54B6ikC
    meta:
      background: BACKGROUND_TRANSPARENT
      width: 2
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-Tyv02UA_6W
    type: COLUMN
  COLUMN-G6_2DvG8aK:
    children:
      - CHART-id4RGv80N-
    id: COLUMN-G6_2DvG8aK
    meta:
      background: BACKGROUND_TRANSPARENT
      width: 2
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-oAtmu5grZ
    type: COLUMN
  DASHBOARD_VERSION_KEY: v2
  GRID_ID:
    children: []
    id: GRID_ID
    parents:
      - ROOT_ID
    type: GRID
  HEADER_ID:
    id: HEADER_ID
    meta:
      text: Sales Dashboard
    type: HEADER
  MARKDOWN--AtDSWnapE:
    children: []
    id: MARKDOWN--AtDSWnapE
    meta:
      code:
        "# \U0001F697 Vehicle Sales Dashboard \U0001F3CD\n\nThis example dashboard\
        \ provides insight into the business operations of vehicle seller. The dataset\
        \ powering this dashboard can be found [here on Kaggle](https://www.kaggle.com/kyanyoga/sample-sales-data).\n\
        \n### Timeline\n\nThe dataset contains data on all orders from the 2003 and\
        \ 2004 fiscal years, and some orders from 2005.\n\n### Products Sold\n\nThis\
        \ shop mainly sells the following products:\n\n- \U0001F697 Classic Cars\n\
        - \U0001F3CE\uFE0F Vintage Cars\n- \U0001F3CD\uFE0F Motorcycles\n- \U0001F69A\
        \ Trucks & Buses \U0001F68C\n- \U0001F6E9\uFE0F Planes\n- \U0001F6A2 Ships\n\
        - \U0001F688 Trains"
      height: 53
      width: 3
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
      - ROW-Tyv02UA_6W
    type: MARKDOWN
  ROOT_ID:
    children:
      - TABS-e5Ruro0cjP
    id: ROOT_ID
    type: ROOT
  ROW-0l1WcDzW3:
    children:
      - CHART-OJ9aWDmn1q
      - CHART-AYpv8gFi_q
    id: ROW-0l1WcDzW3
    meta:
      background: BACKGROUND_TRANSPARENT
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-4fthLQmdX
    type: ROW
  ROW-E7MDSGfnm:
    children:
      - CHART-YFg-9wHE7s
      - CHART-_LMKI0D3tj
    id: ROW-E7MDSGfnm
    meta:
      background: BACKGROUND_TRANSPARENT
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-4fthLQmdX
    type: ROW
  ROW-Tyv02UA_6W:
    children:
      - COLUMN-8Rp54B6ikC
      - CHART-vomBOiI7U9
      - MARKDOWN--AtDSWnapE
    id: ROW-Tyv02UA_6W
    meta:
      background: BACKGROUND_TRANSPARENT
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
    type: ROW
  ROW-oAtmu5grZ:
    children:
      - COLUMN-G6_2DvG8aK
      - CHART-KKT9BsnUst
      - CHART-j24u8ve41b
    id: ROW-oAtmu5grZ
    meta:
      background: BACKGROUND_TRANSPARENT
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
      - TAB-d-E0Zc1cTH
    type: ROW
  TAB-4fthLQmdX:
    children:
      - ROW-0l1WcDzW3
      - ROW-E7MDSGfnm
    id: TAB-4fthLQmdX
    meta:
      text: "\U0001F9ED Exploratory"
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
    type: TAB
  TAB-d-E0Zc1cTH:
    children:
      - ROW-Tyv02UA_6W
      - ROW-oAtmu5grZ
    id: TAB-d-E0Zc1cTH
    meta:
      text: "\U0001F3AF Sales Overview"
    parents:
      - ROOT_ID
      - TABS-e5Ruro0cjP
    type: TAB
  TABS-e5Ruro0cjP:
    children:
      - TAB-d-E0Zc1cTH
      - TAB-4fthLQmdX
    id: TABS-e5Ruro0cjP
    meta: {}
    parents:
      - ROOT_ID
    type: TABS
metadata:
  timed_refresh_immune_slices: []
  expanded_slices: {}
  refresh_frequency: 0
  default_filters: "{}"
  color_scheme: supersetColors
  label_colors:
    Medium: "#1FA8C9"
    Small: "#454E7C"
    Large: "#5AC189"
    SUM(SALES): "#1FA8C9"
    Classic Cars: "#454E7C"
    Vintage Cars: "#5AC189"
    Motorcycles: "#FF7F44"
    Trucks and Buses: "#666666"
    Planes: "#E04355"
    Ships: "#FCC700"
    Trains: "#A868B7"
version: 1.0.0
