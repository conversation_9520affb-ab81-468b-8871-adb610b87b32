# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
dashboard_title: Slack Dashboard
description: null
css: null
slug: null
uuid: 9726d8b0-222f-eb2c-034a-02b9f51ef5fd
position:
  CHART-EYIBwyUiHc:
    children: []
    id: CHART-EYIBwyUiHc
    meta:
      chartId: 2396
      height: 70
      sliceName: Members per Channel
      uuid: d44e416d-1647-44e4-b442-6da34b44adc4
      width: 3
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-PgcMF2PpB
    type: CHART
  CHART-FwpJA_o1-n:
    children: []
    id: CHART-FwpJA_o1-n
    meta:
      chartId: 3055
      height: 44
      sliceName: New Members per Month
      uuid: 92e1d712-bcf9-4d7e-9b94-26cffe502908
      width: 2
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-aseZBdP1v
    type: CHART
  CHART-JhE-Y0xxgi:
    children: []
    id: CHART-JhE-Y0xxgi
    meta:
      chartId: 1908
      height: 44
      sliceName: Top Timezones
      sliceNameOverride: Top Timezones for Members
      uuid: 62b7242e-decc-2d1b-7f80-c62776939d1e
      width: 4
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-aseZBdP1v
    type: CHART
  CHART-RbxP2Dl7Ad:
    children: []
    id: CHART-RbxP2Dl7Ad
    meta:
      chartId: 1905
      height: 44
      sliceName: Weekly Messages
      uuid: abe2c022-ceee-a60a-e601-ab93f7ee52b1
      width: 2
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-aseZBdP1v
    type: CHART
  CHART-cj9KtCNRq3:
    children: []
    id: CHART-cj9KtCNRq3
    meta:
      chartId: 1903
      height: 19
      sliceName: Number of Members
      uuid: 7dad983b-e9f6-d2e8-91da-c2262d4e84e8
      width: 3
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-aseZBdP1v
      - COLUMN-4bvhV9jxDI
    type: CHART
  CHART-ej0FpkKxzj:
    children: []
    id: CHART-ej0FpkKxzj
    meta:
      chartId: 2398
      height: 69
      sliceName: Cross Channel Relationship
      uuid: 6cb43397-5c62-4f32-bde2-95344c412b5a
      width: 5
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-PgcMF2PpB
    type: CHART
  CHART-f42-kMWQPd:
    children: []
    id: CHART-f42-kMWQPd
    meta:
      chartId: 2397
      height: 68
      sliceName: Cross Channel Relationship
      uuid: f2a8731b-3d8c-4d86-9d33-7c0a3e64d21c
      width: 4
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-PgcMF2PpB
    type: CHART
  CHART-tMLHDtzb67:
    children: []
    id: CHART-tMLHDtzb67
    meta:
      chartId: 2395
      height: 85
      sliceName: Messages per Channel
      uuid: b0f11bdf-793f-473f-b7d5-b9265e657896
      width: 12
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-y62Rf2K3m
    type: CHART
  CHART-vIvrauAMxV:
    children: []
    id: CHART-vIvrauAMxV
    meta:
      chartId: 1906
      height: 44
      sliceName: Weekly Threads
      uuid: 9f742bdd-cac1-468c-3a37-35c9b3cfd5bb
      width: 2
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-aseZBdP1v
    type: CHART
  COLUMN-4bvhV9jxDI:
    children:
      - MARKDOWN--8u3tfVF49
      - CHART-cj9KtCNRq3
    id: COLUMN-4bvhV9jxDI
    meta:
      background: BACKGROUND_TRANSPARENT
      width: 2
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-aseZBdP1v
    type: COLUMN
  DASHBOARD_VERSION_KEY: v2
  GRID_ID:
    children:
      - ROW-aseZBdP1v
      - ROW-y62Rf2K3m
      - ROW-PgcMF2PpB
    id: GRID_ID
    parents:
      - ROOT_ID
    type: GRID
  HEADER_ID:
    id: HEADER_ID
    meta:
      text: Slack Dashboard
    type: HEADER
  MARKDOWN--8u3tfVF49:
    children: []
    id: MARKDOWN--8u3tfVF49
    meta:
      code: <img src="https://cdn.brandfolder.io/5H442O3W/at/pl546j-7le8zk-838dm2/Slack_RGB.svg">
      height: 23
      width: 3
    parents:
      - ROOT_ID
      - GRID_ID
      - ROW-aseZBdP1v
      - COLUMN-4bvhV9jxDI
    type: MARKDOWN
  ROOT_ID:
    children:
      - GRID_ID
    id: ROOT_ID
    type: ROOT
  ROW-PgcMF2PpB:
    children:
      - CHART-EYIBwyUiHc
      - CHART-f42-kMWQPd
      - CHART-ej0FpkKxzj
    id: ROW-PgcMF2PpB
    meta:
      "0": ROOT_ID
      background: BACKGROUND_TRANSPARENT
    parents:
      - ROOT_ID
      - GRID_ID
    type: ROW
  ROW-aseZBdP1v:
    children:
      - COLUMN-4bvhV9jxDI
      - CHART-vIvrauAMxV
      - CHART-RbxP2Dl7Ad
      - CHART-FwpJA_o1-n
      - CHART-JhE-Y0xxgi
    id: ROW-aseZBdP1v
    meta:
      background: BACKGROUND_TRANSPARENT
    parents:
      - ROOT_ID
      - GRID_ID
    type: ROW
  ROW-y62Rf2K3m:
    children:
      - CHART-tMLHDtzb67
    id: ROW-y62Rf2K3m
    meta:
      "0": ROOT_ID
      background: BACKGROUND_TRANSPARENT
    parents:
      - ROOT_ID
      - GRID_ID
    type: ROW
metadata:
  timed_refresh_immune_slices: []
  expanded_slices: {}
  refresh_frequency: 0
  default_filters: "{}"
  color_scheme: supersetColors
  label_colors:
    "0": "#1FA8C9"
    "1": "#454E7C"
    introductions: "#5AC189"
    jobs: "#FF7F44"
    apache-releases: "#666666"
    commits: "#E04355"
    dashboard-filters: "#FCC700"
    announcements: "#A868B7"
    general: "#3CCCCB"
    superset_stage_alerts: "#A38F79"
    contributing: "#8FD3E4"
    graduation: "#A1A6BD"
    embedded-dashboards: "#ACE1C4"
    helm-k8-deployment: "#FEC0A1"
    visualization_plugins: "#B2B2B2"
    community-feedback: "#EFA1AA"
    cypress-tests: "#FDE380"
    product_feedback: "#D3B3DA"
    developers: "#9EE5E5"
    dashboard-level-access: "#D1C6BC"
    design: "#1FA8C9"
    feature-requests: "#454E7C"
    localization: "#5AC189"
    newsletter: "#FF7F44"
    beginners: "#666666"
    github-notifications: "#E04355"
    superset-champions: "#FCC700"
    superset_prod_reports: "#A868B7"
    dashboards: "#3CCCCB"
    pull-requests: "#A38F79"
    support: "#8FD3E4"
    globalnav_search: "#A1A6BD"
version: 1.0.0
