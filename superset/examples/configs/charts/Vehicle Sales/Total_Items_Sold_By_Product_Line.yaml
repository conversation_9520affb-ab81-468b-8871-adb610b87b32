# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Total Items Sold (By Product Line)
viz_type: table
params:
  adhoc_filters: []
  all_columns: []
  color_pn: true
  datasource: 23__table
  granularity_sqla: order_date
  groupby:
  - product_line
  metrics:
  - aggregate: SUM
    column:
      column_name: quantity_ordered
      description: null
      expression: null
      filterable: true
      groupby: true
      id: 914
      is_dttm: false
      optionName: _col_QuantityOrdered
      python_date_format: null
      type: BIGINT
      verbose_name: null
    expressionType: SIMPLE
    hasCustomLabel: true
    isNew: false
    label: '# of Products Sold'
    optionName: metric_skdbciwba6g_z1r5w1pxlqj
    sqlExpression: null
  order_by_cols: []
  order_desc: true
  percent_metrics: null
  queryFields:
    groupby: groupby
    metrics: metrics
  query_mode: aggregate
  row_limit: null
  show_cell_bars: true
  slice_id: 2807
  table_timestamp_format: smart_date
  time_grain_sqla: P1D
  time_range: No filter
  url_params: {}
  viz_type: table
cache_timeout: null
uuid: b8b7ca30-6291-44b0-bc64-ba42e2892b86
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
