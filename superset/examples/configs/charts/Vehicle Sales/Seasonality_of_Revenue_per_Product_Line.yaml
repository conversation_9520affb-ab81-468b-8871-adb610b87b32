# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Seasonality of Revenue (per Product Line)
viz_type: horizon
params:
  adhoc_filters: []
  datasource: 23__table
  granularity_sqla: order_date
  groupby:
  - product_line
  horizon_color_scale: series
  metrics:
  - aggregate: SUM
    column:
      column_name: sales
      description: null
      expression: null
      filterable: true
      groupby: true
      id: 917
      is_dttm: false
      optionName: _col_Sales
      python_date_format: null
      type: DOUBLE PRECISION
      verbose_name: null
    expressionType: SIMPLE
    hasCustomLabel: false
    isNew: false
    label: (Sales)
    optionName: metric_e3kxby3hnjs_nfd4adbcnsn
    sqlExpression: null
  order_desc: true
  queryFields:
    groupby: groupby
    metrics: metrics
  row_limit: null
  series_height: '25'
  slice_id: 2811
  time_range: No filter
  url_params: {}
  viz_type: horizon
cache_timeout: null
uuid: cf0da099-b3ab-4d94-ab62-cf353ac3c611
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
