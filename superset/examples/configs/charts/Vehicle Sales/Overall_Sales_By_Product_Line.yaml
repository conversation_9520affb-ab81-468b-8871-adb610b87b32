# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Overall Sales (By Product Line)
viz_type: pie
params:
  adhoc_filters: []
  color_scheme: supersetColors
  datasource: 23__table
  donut: true
  granularity_sqla: order_date
  groupby:
  - product_line
  innerRadius: 41
  label_line: true
  labels_outside: true
  metric:
    aggregate: SUM
    column:
      column_name: sales
      description: null
      expression: null
      filterable: true
      groupby: true
      id: 917
      is_dttm: false
      optionName: _col_Sales
      python_date_format: null
      type: DOUBLE PRECISION
      verbose_name: null
    expressionType: SIMPLE
    hasCustomLabel: false
    isNew: false
    label: (Sales)
    optionName: metric_3sk6pfj3m7i_64h77bs4sly
    sqlExpression: null
  number_format: SMART_NUMBER
  outerRadius: 65
  label_type: key
  queryFields:
    groupby: groupby
    metric: metrics
  row_limit: null
  show_labels: true
  show_labels_threshold: 2
  show_legend: false
  slice_id: 670
  time_range: No filter
  url_params: {}
  viz_type: pie
cache_timeout: null
uuid: 09c497e0-f442-1121-c9e7-671e37750424
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
