# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Unicode Cloud
viz_type: word_cloud
params:
  granularity_sqla: dttm
  groupby: []
  limit: '100'
  metric:
    aggregate: SUM
    column:
      column_name: value
    expressionType: SIMPLE
    label: Value
  rotation: square
  row_limit: 50000
  series: short_phrase
  since: 100 years ago
  size_from: '10'
  size_to: '70'
  until: now
  viz_type: word_cloud
cache_timeout: null
uuid: 609e26d8-8e1e-4097-9751-931708e24ee4
version: 1.0.0
dataset_uuid: a6771c73-96fc-44c6-8b6e-9d303955ea48
