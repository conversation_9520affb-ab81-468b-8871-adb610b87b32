# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Most Dominant Platforms
viz_type: pie
params:
  adhoc_filters:
  - clause: WHERE
    comparator: '25'
    expressionType: SIMPLE
    filterOptionName: filter_znxs4o61aod_n5blgxo29r
    isExtra: false
    isNew: false
    operator: <=
    sqlExpression: null
    subject: rank
  color_scheme: supersetColors
  datasource: 21__table
  donut: true
  granularity_sqla: year
  groupby:
  - publisher
  innerRadius: 45
  label_line: true
  labels_outside: true
  metric:
    aggregate: SUM
    column:
      column_name: global_sales
      description: null
      expression: null
      filterable: true
      groupby: true
      id: 887
      is_dttm: false
      optionName: _col_Global_Sales
      python_date_format: null
      type: DOUBLE PRECISION
      verbose_name: null
    expressionType: SIMPLE
    hasCustomLabel: false
    isNew: false
    label: SUM(Global_Sales)
    optionName: metric_k2rqz0zqhkf_49hu4kq9h3u
    sqlExpression: null
  number_format: SMART_NUMBER
  outerRadius: 67
  label_type: key
  queryFields:
    groupby: groupby
    metric: metrics
  row_limit: null
  show_labels: true
  show_legend: false
  slice_id: 658
  time_range: No filter
  url_params: {}
  viz_type: pie
cache_timeout: null
uuid: 1810975a-f6d4-07c3-495c-c3b535d01f21
version: 1.0.0
dataset_uuid: 53d47c0c-c03d-47f0-b9ac-81225f808283
