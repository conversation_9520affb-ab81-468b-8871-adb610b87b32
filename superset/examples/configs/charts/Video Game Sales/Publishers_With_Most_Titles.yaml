# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Publishers With Most Titles
viz_type: table
params:
  adhoc_filters: []
  all_columns:
  - rank
  - name
  - global_sales
  - platform
  - genre
  - publisher
  - year
  color_pn: false
  datasource: 21__table
  granularity_sqla: year
  groupby: []
  metrics:
  - count
  order_by_cols: []
  order_desc: true
  page_length: null
  percent_metrics: []
  queryFields:
    groupby: groupby
    metrics: metrics
  query_mode: raw
  row_limit: 10
  show_cell_bars: false
  slice_id: 657
  table_timestamp_format: smart_date
  time_grain_sqla: P1D
  time_range: No filter
  url_params: {}
  viz_type: table
cache_timeout: null
uuid: d20b7324-3b80-24d4-37e2-3bd583b66713
version: 1.0.0
dataset_uuid: 53d47c0c-c03d-47f0-b9ac-81225f808283
