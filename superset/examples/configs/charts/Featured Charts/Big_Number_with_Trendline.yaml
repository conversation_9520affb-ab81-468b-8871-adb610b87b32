# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Big Number with Trendline
description: null
certified_by: null
certification_details: null
viz_type: big_number
params:
  datasource: 22__table
  viz_type: big_number
  x_axis: order_date
  time_grain_sqla: P1M
  metric: count
  adhoc_filters:
    - clause: WHERE
      subject: order_date
      operator: TEMPORAL_RANGE
      comparator: No filter
      expressionType: SIMPLE
  show_trend_line: true
  start_y_axis_at_zero: true
  color_picker:
    r: 0
    g: 122
    b: 135
    a: 1
  header_font_size: 0.4
  subheader_font_size: 0.15
  y_axis_format: SMART_NUMBER
  time_format: smart_date
  rolling_type: None
  extra_form_data: {}
  dashboards:
    - 13
cache_timeout: null
uuid: 281656a3-3d8d-4423-8b58-b050d59fadd9
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
