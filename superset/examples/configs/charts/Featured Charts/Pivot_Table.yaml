# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Pivot Table
description: null
certified_by: null
certification_details: null
viz_type: pivot_table_v2
params:
  datasource: 22__table
  viz_type: pivot_table_v2
  groupbyColumns:
    - product_line
  groupbyRows:
    - country
    - city
  time_grain_sqla: P1D
  temporal_columns_lookup:
    order_date: true
  metrics:
    - expressionType: SIMPLE
      column:
        advanced_data_type: null
        certification_details: null
        certified_by: null
        column_name: sales
        description: null
        expression: null
        filterable: true
        groupby: true
        id: 734
        is_certified: false
        is_dttm: false
        python_date_format: null
        type: DOUBLE PRECISION
        type_generic: 0
        verbose_name: null
        warning_markdown: null
      aggregate: SUM
      sqlExpression: null
      datasourceWarning: false
      hasCustomLabel: false
      label: SUM(sales)
      optionName: metric_psnoaxdky5h_ojfgcygk97c
  metricsLayout: COLUMNS
  adhoc_filters:
    - clause: WHERE
      subject: order_date
      operator: TEMPORAL_RANGE
      comparator: No filter
      expressionType: SIMPLE
  row_limit: 10000
  order_desc: true
  aggregateFunction: Sum
  valueFormat: SMART_NUMBER
  date_format: smart_date
  rowOrder: key_a_to_z
  colOrder: key_a_to_z
  extra_form_data: {}
  dashboards:
    - 13
cache_timeout: null
uuid: fcf2a0cf-0079-4f59-854f-d28297b07bf0
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
