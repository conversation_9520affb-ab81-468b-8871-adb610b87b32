# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Radar
description: null
certified_by: null
certification_details: null
viz_type: radar
params:
  datasource: 22__table
  viz_type: radar
  groupby:
    - product_line
  metrics:
    - count
    - expressionType: SIMPLE
      column:
        advanced_data_type: null
        certification_details: null
        certified_by: null
        column_name: price_each
        description: null
        expression: null
        filterable: true
        groupby: true
        id: 733
        is_certified: false
        is_dttm: false
        python_date_format: null
        type: DOUBLE PRECISION
        type_generic: 0
        verbose_name: null
        warning_markdown: null
      aggregate: AVG
      sqlExpression: null
      datasourceWarning: false
      hasCustomLabel: false
      label: AVG(price_each)
      optionName: metric_ethqy44wrel_gsqbm609hxt
    - expressionType: SIMPLE
      column:
        advanced_data_type: null
        certification_details: null
        certified_by: null
        column_name: sales
        description: null
        expression: null
        filterable: true
        groupby: true
        id: 734
        is_certified: false
        is_dttm: false
        python_date_format: null
        type: DOUBLE PRECISION
        type_generic: 0
        verbose_name: null
        warning_markdown: null
      aggregate: AVG
      sqlExpression: null
      datasourceWarning: false
      hasCustomLabel: false
      label: AVG(sales)
      optionName: metric_r5emvf2ybfe_blvnta3absu
  adhoc_filters:
    - clause: WHERE
      subject: order_date
      operator: TEMPORAL_RANGE
      comparator: No filter
      expressionType: SIMPLE
  row_limit: 10
  color_scheme: supersetColors
  show_legend: true
  legendType: scroll
  legendOrientation: top
  legendMargin: ""
  show_labels: false
  label_type: value
  label_position: top
  number_format: SMART_NUMBER
  date_format: smart_date
  is_circle: true
  extra_form_data: {}
  dashboards: []
cache_timeout: null
uuid: 1be00870-89b8-4ba0-a451-1fe56ef89581
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
