# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Tree
description: null
certified_by: null
certification_details: null
viz_type: tree_chart
params:
  datasource: 8__table
  viz_type: tree_chart
  id: id
  parent: parent
  name: name
  root_node_id: "1"
  metric: count
  adhoc_filters: []
  row_limit: 1000
  layout: radial
  node_label_position: top
  child_label_position: top
  symbol: emptyCircle
  symbolSize: 7
  roam: false
  extra_form_data: {}
  dashboards: []
cache_timeout: null
uuid: 57e7f05e-0c9a-4313-8a33-e6d0999824af
version: 1.0.0
dataset_uuid: f710a997-c65e-4aa6-aaed-b7d6998565ae
