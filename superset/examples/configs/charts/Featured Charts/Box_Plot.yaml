# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Box Plot
description: null
certified_by: null
certification_details: null
viz_type: box_plot
params:
  datasource: 22__table
  viz_type: box_plot
  columns:
    - order_date
  time_grain_sqla: P1D
  temporal_columns_lookup:
    order_date: true
  groupby:
    - product_line
  metrics:
    - count
  adhoc_filters:
    - clause: WHERE
      subject: order_date
      operator: TEMPORAL_RANGE
      comparator: No filter
      expressionType: SIMPLE
  whiskerOptions: Tukey
  x_axis_title_margin: 15
  y_axis_title_margin: 15
  y_axis_title_position: Left
  color_scheme: supersetColors
  x_ticks_layout: auto
  number_format: SMART_NUMBER
  date_format: smart_date
  extra_form_data: {}
  dashboards:
    - 13
cache_timeout: null
uuid: cc8eea37-f890-4816-8df0-b1354d7cd553
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
