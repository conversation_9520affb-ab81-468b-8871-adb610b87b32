# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: TreeMap
description: null
certified_by: null
certification_details: null
viz_type: treemap_v2
params:
  datasource: 23__table
  viz_type: treemap_v2
  groupby:
    - year
    - product_line
  metric: count
  row_limit: 10000
  adhoc_filters:
    - clause: WHERE
      subject: order_date
      operator: TEMPORAL_RANGE
      comparator: No filter
      expressionType: SIMPLE
  color_scheme: supersetColors
  show_labels: true
  show_upper_labels: true
  label_type: key_value
  number_format: SMART_NUMBER
  date_format: smart_date
  extra_form_data: {}
  dashboards: []
query_context: null
cache_timeout: null
uuid: 36367ecc-e09a-4c9f-80df-5dbaf97f741f
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
