# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Gauge
description: null
certified_by: null
certification_details: null
viz_type: gauge_chart
params:
  datasource: 22__table
  viz_type: gauge_chart
  groupby:
    - deal_size
  metric: count
  adhoc_filters:
    - clause: WHERE
      subject: order_date
      operator: TEMPORAL_RANGE
      comparator: No filter
      expressionType: SIMPLE
  row_limit: 10
  start_angle: 225
  end_angle: -45
  color_scheme: supersetColors
  font_size: 13
  number_format: SMART_NUMBER
  value_formatter: "{value}"
  show_pointer: true
  animation: true
  show_axis_tick: false
  show_split_line: false
  split_number: 10
  show_progress: true
  overlap: true
  round_cap: false
  extra_form_data: {}
  dashboards: []
cache_timeout: null
uuid: 57ccf0f1-d28a-4127-9581-7153c5a15b62
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
