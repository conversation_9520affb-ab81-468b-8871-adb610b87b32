# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Histogram
description: null
certified_by: null
certification_details: null
viz_type: histogram_v2
params:
  datasource: 22__table
  viz_type: histogram_v2
  slice_id: 130
  column: quantity_ordered
  groupby:
    - deal_size
  adhoc_filters:
    - clause: WHERE
      comparator: No filter
      expressionType: SIMPLE
      operator: TEMPORAL_RANGE
      subject: order_date
  row_limit: 10000
  bins: 10
  normalize: false
  color_scheme: supersetAndPresetColors
  show_value: false
  show_legend: true
  extra_form_data: {}
  dashboards: []
cache_timeout: null
uuid: 2a89c19d-d0d2-4f5e-9007-e0fb65440e80
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
