# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Vaccine Candidates per Country
viz_type: world_map
params:
  adhoc_filters: []
  color_picker:
    a: 1
    b: 135
    g: 122
    r: 0
  country_fieldtype: name
  datasource: 14__table
  entity: country_name
  linear_color_scheme: schemeYlOrBr
  max_bubble_size: '25'
  metric: count
  row_limit: 10000
  secondary_metric:
    aggregate: COUNT
    column:
      column_name: country_name
      description: null
      expression: null
      filterable: true
      groupby: true
      id: 583
      is_dttm: false
      python_date_format: null
      type: TEXT
      verbose_name: null
    expressionType: SIMPLE
    hasCustomLabel: false
    isNew: false
    label: COUNT(Country_Name)
    optionName: metric_i8otphezfu_5urmjjs7c1
    sqlExpression: null
  show_bubbles: true
  time_range: No filter
  url_params: {}
  viz_type: world_map
cache_timeout: null
uuid: ddc91df6-fb40-4826-bdca-16b85af1c024
version: 1.0.0
dataset_uuid: 974b7a1c-22ea-49cb-9214-97b7dbd511e0
