# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Vaccine Candidates per Country
viz_type: treemap_v2
params:
  adhoc_filters: []
  color_scheme: presetColors
  datasource: 69__table
  groupby:
    - country_name
  label_colors: {}
  metric: count
  number_format: SMART_NUMBER
  queryFields:
    groupby: groupby
    metrics: metrics
  row_limit: 10000
  time_range: No filter
  treemap_ratio: 1.618033988749895
  url_params: {}
  viz_type: treemap_v2
cache_timeout: null
uuid: e2f5a8a7-feb0-4f79-bc6b-01fe55b98b3c
version: 1.0.0
dataset_uuid: 974b7a1c-22ea-49cb-9214-97b7dbd511e0
