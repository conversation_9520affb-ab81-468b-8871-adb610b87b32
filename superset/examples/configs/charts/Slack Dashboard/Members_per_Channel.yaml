# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Members per Channel
viz_type: treemap_v2
params:
  adhoc_filters: []
  color_scheme: supersetColors
  datasource: 57__table
  groupby:
    - channel_name
  label_colors: {}
  metric: count
  number_format: SMART_NUMBER
  queryFields:
    groupby: groupby
    metrics: metrics
  row_limit: null
  slice_id: 2396
  time_range: No filter
  treemap_ratio: 1.618033988749895
  url_params: {}
  viz_type: treemap_v2
cache_timeout: null
uuid: d44e416d-1647-44e4-b442-6da34b44adc4
version: 1.0.0
dataset_uuid: 3d9c0054-b31b-4102-92de-b1ef9f9e5e77
