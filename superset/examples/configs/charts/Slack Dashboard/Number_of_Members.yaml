# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Number of Members
viz_type: big_number_total
params:
  adhoc_filters: []
  datasource: 31__table
  granularity_sqla: updated
  header_font_size: 0.4
  metric: count
  queryFields:
    metric: metrics
  subheader: Slack Members
  subheader_font_size: 0.125
  time_range: No filter
  url_params: {}
  viz_type: big_number_total
  y_axis_format: SMART_NUMBER
cache_timeout: null
uuid: 7dad983b-e9f6-d2e8-91da-c2262d4e84e8
version: 1.0.0
dataset_uuid: 7195db6b-2d17-7619-b7c7-26b15378df8c
