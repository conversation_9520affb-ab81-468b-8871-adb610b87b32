# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: New Members per Month
viz_type: big_number
params:
  adhoc_filters: []
  color_picker:
    a: 1
    b: 135
    g: 122
    r: 0
  compare_lag: 1
  compare_suffix: MoM
  datasource: 66__table
  granularity_sqla: date
  header_font_size: 0.4
  metric:
    aggregate: SUM
    column:
      column_name: new_members
      description: null
      expression: null
      filterable: true
      groupby: true
      id: 1849
      is_dttm: false
      optionName: _col_New Members
      python_date_format: null
      type: BIGINT
      verbose_name: null
    expressionType: SIMPLE
    hasCustomLabel: false
    isNew: false
    label: SUM(New Members)
    optionName: metric_7pksdivmphp_bicn1fji7en
    sqlExpression: null
  queryFields:
    metric: metrics
  rolling_type: None
  show_trend_line: true
  slice_id: 3055
  start_y_axis_at_zero: true
  subheader_font_size: 0.15
  time_grain_sqla: P1M
  time_range: No filter
  url_params: {}
  viz_type: big_number
  y_axis_format: SMART_NUMBER
cache_timeout: null
uuid: 92e1d712-bcf9-4d7e-9b94-26cffe502908
version: 1.0.0
dataset_uuid: 9dd99cda-ff6b-4575-9a74-684d06e871ab
