# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Top 15 Languages Spoken at Home
viz_type: table
params:
  adhoc_filters:
  - clause: WHERE
    comparator: 'NULL'
    expressionType: SIMPLE
    filterOptionName: filter_ameaka2efjv_rfv1et5nwng
    isExtra: false
    isNew: false
    operator: '!='
    sqlExpression: null
    subject: lang_at_home
  all_columns: []
  color_pn: true
  datasource: 42__table
  granularity_sqla: time_start
  groupby:
  - lang_at_home
  metrics:
  - count
  order_by_cols: []
  order_desc: true
  percent_metrics: []
  queryFields:
    groupby: groupby
    metrics: metrics
  query_mode: aggregate
  row_limit: '15'
  show_cell_bars: true
  slice_id: 1386
  table_timestamp_format: smart_date
  time_grain_sqla: P1D
  time_range: No filter
  url_params: {}
  viz_type: table
cache_timeout: null
uuid: 03a74c97-52fc-cf87-233c-d4275f8c550c
version: 1.0.0
dataset_uuid: d95a2865-53ce-1f82-a53d-8e3c89331469
