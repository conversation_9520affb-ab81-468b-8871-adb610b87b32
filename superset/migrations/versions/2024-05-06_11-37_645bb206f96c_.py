# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
"""empty message

Revision ID: 645bb206f96c
Revises: ('58d051681a3b', '3dfd0e78650e')
Create Date: 2024-05-06 11:37:23.089047

"""

# revision identifiers, used by Alembic.
revision = "645bb206f96c"
down_revision = ("58d051681a3b", "3dfd0e78650e")


def upgrade():
    pass


def downgrade():
    pass
