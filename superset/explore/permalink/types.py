# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
from typing import Any, Optional, TypedDict


class ExplorePermalinkState(TypedDict, total=False):
    formData: dict[str, Any]
    urlParams: Optional[list[tuple[str, str]]]


class ExplorePermalinkValue(TypedDict):
    chartId: Optional[int]
    # either datasetId or datasourceId is required
    # TODO: deprecated - datasetId is deprecated
    # and should be removed in next major release
    datasetId: Optional[int]
    datasourceId: Optional[int]
    datasourceType: str
    datasource: str
    state: ExplorePermalinkState
